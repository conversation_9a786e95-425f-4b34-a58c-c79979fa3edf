<template>
    <div v-loading="loading">
        <el-page-header @back="back" :icon="ArrowLeft" class="p-3 bg-white border-b absolute w-full z-10 h-[96%]">
            <template #content>
                <span class="text-large font-600 mr-3"> 编辑发布内容 </span>
            </template>
            <el-scrollbar class="bg-white p-2">
                <div class="mb-10">
                    <el-form ref="releaseFormRef" :model="release">
                        <el-divider content-position="left">
                            <div class="flex gap-2 items-center justify-center">
                                <el-icon class="text-2xl text-blue-500">
                                    <star-filled />

                                </el-icon>
                                <h2>发布信息</h2>
                            </div>

                        </el-divider>
                        <div class="p-2 border-2 border-gray-100 mr-2">
                            <el-row>
                                <el-col :span="3">
                                    <el-form-item>
                                        <image-upload v-model="release.image" :isShowTip="false" :limit="1" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="21">
                                    <el-row>
                                        <el-col :span="8">
                                            <el-form-item label="栏目" class="font-semibold" prop="columnId"
                                                :rules="[{ required: true, message: '请选择选择栏目' }]" label-width="60px">
                                                <tree-select v-if="refreshColumn" v-model:value="release.columnId"
                                                    :options="getColumnList" class="font-normal"
                                                    :objMap="{ value: 'id', label: 'columnName', children: 'children' }"
                                                    placeholder="请选择选择栏目" style="width:100%" />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="16">
                                            <el-form-item label="来源" prop="source" label-width="60px">
                                                <el-input v-model="release.source" maxlength="200" placeholder="请输入来源">
                                                </el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="24">
                                            <el-form-item label="标题" prop="title"
                                                :rules="[{ required: true, message: '请输入标题' }]" label-width="60px">
                                                <el-input v-model="release.title" maxlength="200" placeholder="请输入标题">
                                                </el-input>
                                            </el-form-item>
                                        </el-col>

                                        <el-col :span="8">
                                            <el-form-item label="子标题" prop="titleSub" label-width="60px">
                                                <el-input v-model="release.titleSub" maxlength="200"
                                                    placeholder="请输入子标题">
                                                </el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label=" 日期" label-width="80px">
                                                <el-date-picker v-model="release.articleDate" type="datetime"
                                                    placeholder="请选择日期" value-format="YYYY-MM-DD HH:mm:ss"
                                                    format="YYYY-MM-DD HH:mm:ss" style="width:100%" />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="显示顺序" label-width="80px">
                                                <el-input-number v-model="release.sort" maxlength="9"
                                                    placeholder="请输入显示顺序" class="w-full">
                                                </el-input-number>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="8">
                                            <el-form-item label=" 分类标签" prop="tag" label-width="80px">
                                                <el-input v-model="release.tag" placeholder="请输入分类标签">
                                                </el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="16">
                                            <el-form-item label="发布者" label-width="80px">
                                                <el-input v-model="release.releasePerson" placeholder="请输入发布者">
                                                </el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-col>
                                <el-col :span="24">
                                    <el-row>
                                        <el-form-item label="附件" class="!w-full">
                                            <TheUploader ref="uploadRef"
                                                :obj-id="release.mapperId ? release.mapperId : release.id"
                                                accept="application/pdf" :before-upload="handleBeforeUpload">
                                                <template #tooltip>
                                                    <span>请上传pdf文件</span>
                                                </template>

                                            </TheUploader>
                                        </el-form-item>
                                    </el-row>
                                </el-col>
                                <el-col :span="24">
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="摘要" prop="describes">
                                                <el-input v-model="release.describes" type="textarea"
                                                    :autosize="{ minRows: 2, maxRows: 4 }"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-col>
                            </el-row>
                        </div>

                        <el-divider content-position="left" class="mt-7">
                            <div class="flex gap-2 items-center justify-center">
                                <el-icon class="text-2xl text-blue-500">
                                    <star-filled />

                                </el-icon>
                                <h2>发布内容</h2>
                            </div>

                        </el-divider>
                        <div class="p-2 border-2 border-gray-100 mr-2">
                            <!-- <Editor v-model="release.content" :minHeight="400"></Editor> -->
                            <TinyMce v-model="release.content"></TinyMce>
                        </div>

                    </el-form>
                </div>
            </el-scrollbar>


        </el-page-header>
        <div class="absolute bottom-0 w-full bg-gray-50 p-2 border-t z-10 flex justify-center">
            <template v-if="release.processId">
                <el-button type="primary" @click="submit('0')">保存</el-button>
                <!-- <el-button type="success" @click="submit('1')">保存</el-button> -->
            </template>
            <template v-else>
                <el-button type="primary" @click="submit('0')">保存</el-button>
                <el-button v-if="release.releaseStatus != '2'" type="success" @click="submit('1')">提交</el-button>
            </template>
            <el-button type="info" @click="back">关闭</el-button>
        </div>
        <el-dialog title="申请表" v-model="applyDialog" width="50%" append-to-body destroy-on-close draggable>
            <WindowForm :releaseId="applyId" :columnId="columnId" @close="applyDialog = false;"></WindowForm>
        </el-dialog>

        <!-- <el-button type="primary" @click="handlePrint">打印</el-button> -->

    </div>

</template>

<script lang="ts" setup>
// @ts-ignore
import useUserStore from '@/store/modules/user';
import WindowForm from './windowPage.vue';
import TheUploader from '@/components/TheUploader.vue';
import { ArrowLeft } from '@element-plus/icons';
import { getReleaseById, saveRelease, updateRelease, submitAudit, auditRelease } from '@/api/modules/release';
import { dayjs, FormInstance, ElMessage } from 'element-plus';
import { elConfirm, elSuccess } from '@/utils/elmessage';
import { computed, reactive, ref, watch } from 'vue';
import { searchColumns } from '../contColumn/column';
import { useRoute, useRouter } from 'vue-router';
import { filterColumnTree } from '@/utils/common';
import { showInReleaseModules } from '@/utils/release';
// @ts-ignore
import auth from '@/plugins/auth'
import TinyMce from '@/components/TinyMce/index.vue'
import { v4 as uuidv4 } from 'uuid'



const { searchData: searchColumn, columnList } = searchColumns()
const getColumnList = computed(() => {
    return filterColumnTree(columnList, (node) => {
        return node;
    })
})
const userStore = useUserStore()
// const baseForm = ref()
const route = useRoute()
const router = useRouter()
const type = route.query.type as unknown as string
const loading = ref(false)
const refreshColumn = ref(false)
const applyDialog = ref(false)
const applyForm = ref<IReleasePublish>({} as any);
const applyId = ref()
const columnId = ref()
const processInfo = ref()

const getGroupIds = computed(() => {
    return processInfo.value?.currentGroupIdList ?? []
})

const release = reactive<IRelease>({
    id: route.query.id ? route.query.id : uuidv4().replace(/-/g, ''),
    columnId: route.query.columnId,
    releasePerson: userStore.name,
    articleDate: dayjs(Date.now()).format('YYYY-MM-DD HH:mm:ss')
} as IRelease)

const uploadRef = ref()
const handleBeforeUpload = () => {
    const fileList = uploadRef.value.getFileList();
    if (fileList.length >= 3) {
        ElMessage.error('附件最多上传3个');
        return false;
    }
    return true
}

if (route.query.id) {
    getReleaseById(route.query.id as string).then(res => {
        release.id = res.data.id
        release.title = res.data.title
        release.titleSub = res.data.titleSub
        release.tag = res.data.tag
        release.sort = res.data.sort
        release.releaseStatus = res.data.releaseStatus
        release.describes = res.data.describes
        release.columnId = res.data.columnId
        release.content = res.data.content
        release.releasePerson = res.data.releasePerson
        release.source = res.data.source
        release.processId = res.data.processId
        console.log(res.data.articleDate, 'res.data.articleDate')
        release.articleDate = res.data.articleDate != null ? dayjs(res.data.articleDate).format('YYYY-MM-DD HH:mm:ss') : dayjs(release.articleDate).format('YYYY-MM-DD HH:mm:ss')
        // release.tagIdList = res.data.tagIdList
        release.image = res.data.image
        processInfo.value = res.data.processInfo
        handleBeforeUpload();
    }).finally(() => {
        searchColumn().then(() => {
            refreshColumn.value = true
            // searchTag()
            // watch(() => release.columnId, () => {
            //     release.tagIdList.length = 0
            //     searchTag(release.columnId)
            // })
        })
    })
} else {
    searchColumn().then(() => {
        refreshColumn.value = true
        // searchTag()
    })
}



const back = () => {
    router.back()
}

const releaseFormRef = ref()
const submit = (saveType: string): Promise<string> => {
    return releaseFormRef.value.validate(async (valid: boolean) => {
        if (valid) {
            loading.value = true
            let save = saveRelease
            if (release.id) save = updateRelease

            save(release).then(async (res) => {
                console.log(res, "res");
                if (saveType == '1') {
                    await submitAudit([res.data])
                    // elSuccess('提交审核成功')
                    back()
                }
                else {
                    // if (res.msg == "true") {
                    //     console.log("申请记录保存成功");
                    //     applyId.value = res.data;
                    //     columnId.value = release.columnId;
                    //     applyDialog.value = true;
                    //     return;
                    // }
                    elSuccess('保存成功')
                    back()
                }
            }).finally(() => {
                loading.value = false
            })

        }
    })

}

enum AuditStatus {
    approval = '同意',
    noapproval = '不同意',
    reject = '撤回'
}

const auditInfo = ref({
    releaseId: route.query.id as string,
    status: AuditStatus.approval,
    reason: '同意'
})
const handleAuditStatusChange = () => {
    if (auditInfo.value.status == AuditStatus.approval) {
        auditInfo.value.reason = '同意'
    } else if (auditInfo.value.status == AuditStatus.noapproval) {
        auditInfo.value.reason = '不同意'
    } else {
        auditInfo.value.reason = '撤回'
    }
}
const audit = () => {
    loading.value = true
    auditRelease(auditInfo.value).then(() => {
        elSuccess('审核成功')
        back()
    }).finally(() => {
        loading.value = false
    })

}

// 打印功能
const handlePrint = () => {
    window.print();
};

</script>

<style scoped>
:deep(.el-page-header__main) {
    height: 100%;
}
</style>