<template>
    <div class="h-full">
        <div class="p-2 flex gap-2 h-full bg-[#f0f2f5]">
            <ColumnTree @node-clicked="handlerNodeClicked" @filter-column="handleFilter" class="w-60"></ColumnTree>
            <div class="flex-1 h-full">
                <el-tabs v-model="activeTab" @tab-change="handleTabChange" type="border-card" class="h-full">
                    <el-tab-pane name="todo" class="h-full">
                        <template #label>
                            <!-- <el-badge :value="toDoCount" :hidden="toDoCount == 0"> -->
                            <el-badge>
                                待审核
                            </el-badge>
                        </template>
                        <todo ref="todoRef" :columnInfo="columnInfo" :tagList="tagList"
                            @totalChange="(val: number) => getTodoCount()" class="overflow-y-auto h-full"></todo>
                    </el-tab-pane>
                    <el-tab-pane name="draft" label="草稿" class="h-full">
                        <draft ref="draftRef" :columnInfo="columnInfo" :tagList="tagList"
                            @totalChange="(val: number) => getTodoCount()" class="overflow-y-auto h-full"></draft>
                    </el-tab-pane>
                    <el-tab-pane name="released" label="待发布" class="h-full">
                        <released ref="releasedRef" :columnInfo="columnInfo" :tagList="tagList"
                            @totalChange="(val: number) => getToReleaseCount()" class="overflow-y-auto h-full">
                        </released>
                    </el-tab-pane>
                    <el-tab-pane name="finishReleased" label="已发布" class="h-full">
                        <finishReleased ref="finishReleasedRef" :columnInfo="columnInfo" :tagList="tagList"
                            @totalChange="(val: number) => getToReleaseCount()" class="overflow-y-auto h-full">
                        </finishReleased>
                    </el-tab-pane>
                </el-tabs>

            </div>
        </div>
    </div>
</template>

<script lang="ts" setup name="Release">
import ColumnTree from '@/components/ColumnTree/index.vue';
import { searchTags } from '../contColumn/column';
import { debounce } from 'lodash';
import { nextTick, onActivated, onMounted, reactive, ref } from 'vue';
import draft from './component/draft.vue'
import todo from './component/todo.vue'
import released from './component/released.vue'
import finishReleased from './component/finishReleased.vue'
import { filter, foreach, map } from 'tree-lodash';
import { getTodoList, getToReleaseList } from '@/api/modules/release';
// @ts-ignore
import auth from '@/plugins/auth'

const activeTab = ref('todo')

const draftRef = ref()
const todoRef = ref()
const finishReleasedRef = ref()
const releasedRef = ref()

const { searchData, tagList } = searchTags()
const columnInfo = ref<{
    columnId: string,
    columnIdList: string[]
}>({ columnIdList: [] } as any)
const handlerNodeClicked = debounce((column: IColumnTree) => {
    columnInfo.value.columnId = column.id
    columnInfo.value.columnIdList!.length = 0
    foreach(column as any, (item => {
        columnInfo.value.columnIdList.push(item.id)
    }))
    search()
    searchData(column.id)
}, 1000)

const handleFilter = debounce((columnIds: string[]) => {
    columnInfo.value.columnIdList!.length = 0
    columnInfo.value.columnIdList!.push(...columnIds)
    search()
}, 1000)

const handleTabChange = () => {
    search()
}

const search = () => {
    nextTick(() => {
        if (activeTab.value == 'draft') {
            draftRef.value.search()
        }
        if (activeTab.value == 'todo') {
            todoRef.value.search()
        }
        if (activeTab.value == 'released') {
            releasedRef.value.search()
        }
        if (activeTab.value == 'finishReleased') {
            finishReleasedRef.value.search()
        }
    })

}

const toDoCount = ref(0)
const getTodoCount = () => {
    let groupId;
    if (auth.hasRole('AUDIT_ROLE')) {
        groupId = 'AUDIT_ROLE'
    }
    if (auth.hasRole('EDIT_ROLE')) {
        groupId = 'EDIT_ROLE'
    }
    if (auth.hasRole('AUDIT_ROLE') && auth.hasRole('EDIT_ROLE')) {
        groupId = ''
    }
    getTodoList({ pageNum: 1, pageSize: 0, groupId: groupId }).then(res => toDoCount.value = res.total)
}
const toReleaseCount = ref(0)
const getToReleaseCount = () => {
    getToReleaseList({ pageNum: 1, pageSize: 10 }).then(res => toReleaseCount.value = res.total)
}
onMounted(() => {
    getTodoCount()
    getToReleaseCount()
})

</script>
<style scoped>
:deep(.el-tabs__content) {
    height: calc(100% - 30px);
}

:deep(.el-tabs--border-card>.el-tabs__content) {
    padding: 0;
}
</style>