<template>
    <div class="mx-auto w-[700px] text-center my-[15px]">
        <h1 class="text-[25px] font-bold leading-[70px]">上海市核电办公室门户网站信息发布审核登记表({{ new Date().getFullYear() }})</h1>
        <table class="table table-auto border-collapse border border-slate-400 w-full" v-loading="loading">
            <tr>
                <th rowspan="2" class="border border-[#222]">栏目名称</th>
                <th class="border border-[#222]">一级栏目</th>
                <th class="border border-[#222]" colspan="2">二级栏目</th>
            </tr>
            <tr>
                <td class="border border-[#222]" ref="columnFst"></td>
                <td class="border border-[#222]" colspan="2" ref="columnSend"></td>
            </tr>
            <tr>
                <th class="border border-[#222]">序号</th>
                <th colspan="3" class="border border-[#222] relative">标题 <button
                        class="absolute text-[14px] right-[20px] text-[#469EE4] ">+添加</button></th>
            </tr>
            <!-- <tr>
                    <td class="border border-[#222]">1</td>
                    <td colspan="3" class="border border-[#222] text-left pl-[10px]">我国首款超长寿命碳-14核电池研制成功</td>
                </tr> -->
            <tr v-for="(item, index) in dataList" :key="index">
                <td class="border border-[#222]">{{ index + 1 }}</td>
                <td colspan="3" class="border border-[#222] text-left pl-[10px]">
                    {{ item.title }} <button v-if="dataList.length > 1"
                        class="absolute text-[14px] right-[20px] text-[#469EE4] ">删除</button>
                </td>
            </tr>
            <tr>
                <th class="border border-[#222]">编 辑</th>
                <td class="border border-[#222]  w-[150px]"></td>
                <th class="border border-[#222] w-[100px]">校 对</th>
                <td class="border border-[#222]  w-[150px]"></td>
            </tr>
            <tr>
                <th class="border border-[#222]">申请时间</th>
                <td class="border border-[#222]  w-[230px]"></td>
                <th class="border border-[#222] w-[100px]">发布时间</th>
                <td class="border border-[#222]  w-[230px]"></td>
            </tr>
            <tr>
                <td colspan="4" class="border border-[#222] text-left px-[15px] pb-[40px]" style="line-height: 30px;">
                    <p class="mt-[10px]">信息内容：</p>
                    <div v-for="(item, index) in dataList" :key="index" class="mb-[40px]">
                        <h2 class="text-center text-[18px] font-[500] mt-[20px]">{{ item.releaseTitle }}</h2>
                        <p class="text-center pb-[5px]">{{ item.releaseSource }}</p>
                        <p class="indent-8">{{ item.releaseDescribes }}</p>
                    </div>
                    <!-- <div id="contentss">
                        <h2 class="text-center text-[18px] font-[500] mt-[20px] ">我国首款超长寿命碳-14核电池研制成功</h2>
                        <p class="text-center pb-[5px]">核电网</p>
                        <p class="indent-8">
                            3月9日，西北师范大学联合相关企业发布国内首款碳-14（C-14）核电池原型机“烛龙一号”，标志着我国在核能技术领域与微型核电池领域取得重大突破。</p>
                        <p class="indent-8">
                            这是国际上首款以碳化硅半导体材料及高比活度C-14为基础的高功率核电池，具有在-100℃至200℃极端温度适应性及2200mWh/g超高能量密度，可广泛适用于医疗、物联网以及太空探测等领域，将实现以绿色低碳属性推动我国新能源产业链迭代升级。
                        </p>

                        <h2 class="text-center text-[18px] font-[500] mt-[20px]">“夸父”关键子系统顺利通过验收</h2>
                        <p class="text-center pb-[5px]">科技日报</p>
                        <p class="indent-8">
                            3月9日，国家“十三五”规划中的重大科技基础设施—聚变堆主机关键系统综合研究设施（CRAFT，又名夸父）关键子系统-1/8真空室及总体安装平台通过专家组测试与验收，标志着系统研制水平及运行能力达到国际先进水平。
                        </p>
                        <p class="indent-8">
                            该平台主体为D型截面双层壳体结构，总高20米，D型截面高11米，最大环向直径19.5米，真空室壳体采用50毫米厚的超低碳不锈钢材料，重达295吨。从预研到正式建成并通过验收，整个过程历时十年。随着CRAFT各子系统的相继研制成功及投入运行，将逐步形成从基础研究到技术验证和工程应用的完整链条，为聚变堆的设计、建设、运行奠定坚实的科学技术基础。
                        </p>

                        <h2 class="text-center text-[18px] font-[500] mt-[20px]">关于“小型模块堆和微堆”的国际研讨会在海口召开</h2>
                        <p class="text-center pb-[5px]">中国核电</p>
                        <p class="indent-8">
                            近日，由国际原子能机构与国家原子能机构联合主办、中国核电旗下海南核电承办的“关于小型模块堆和微堆的规范和标准、设计工程、组件测试和制造以及供应链的跨地区研讨会”在海口市举办。</p>
                        <p class="indent-8">本次研讨会是落实2024年10月海南核电与IAEA签署《小堆能力建设执行协议》的重要活动之一，聚焦技术规范、供应链、设计工程等方面。</p>
                    </div> -->
                </td>
            </tr>
            <tr>
                <td colspan="4" class="border border-[#222] text-left ">
                    <p class="pl-[20px] font-bold">部门负责人意见</p>
                    <p class="pl-[100px]">不涉及国家秘密或敏感信息。</p>
                    <p class="pl-[300px] text-[20px] text-[18px] font-[500]">同意</p>
                    <div class="text-right ">签名：<span class="inline-block w-[100px]"></span> 日期：<span
                            class="inline-block w-[120px]"></span> </div>
                </td>
            </tr>
        </table>
        <div class="bottom-0 w-full bg-gray-50 p-2 border-t z-10 flex justify-center">
            <el-button type="primary" @click="print">打印</el-button>
            <el-button type="danger" @click="emit('close')">取消</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import './js/windowPage.js'
import { getNoReleasedList, getReleaseTodoList } from '@/api/modules/publish'
import { onActivated, reactive, ref, watch, onMounted } from 'vue';

const props = defineProps<{
    releaseId: string,
    columnId: string
}>()

const emit = defineEmits(['close'])

const releaseIds = reactive<string[]>([]);

const searchForm = reactive<Partial<IReleasePublish>>({
    columnId: props.columnId,
    releaseIds: releaseIds,
})

// 获取DOM元素的引用
const columnFst = ref<HTMLElement | null>(null);
const columnSend = ref<HTMLElement | null>(null);

const dataList = reactive<IReleasePublish[]>([])
const releaseList = reactive<IRelease[]>([])
const loading = ref(false)

//获取申请表集合
const getTodoList = (ids: string[]) => {
    loading.value = true
    getReleaseTodoList(ids).then(res => {
        dataList.length = 0
        dataList.push(...res.data)
        console.log(dataList)
        if (Array.from(dataList.values()).length > 0) {
            const firstItem = Array.from(dataList.values())[0];
            console.log(firstItem, 'firstItem');
            if (columnFst.value) {
                columnFst.value.textContent = firstItem.moduleFstName || '';
            }

            if (columnSend.value) {
                columnSend.value.textContent = firstItem.moduleSendName || '';
            }

        }

    }).finally(() => {
        loading.value = false
    })
}

//获取同栏目下其他待提交的信息发布内容
const getNoReleased = (release: Partial<IRelease>) => {
    getNoReleasedList(searchForm).then(res => {
        releaseList.length = 0
        releaseList.push(...res.rows)
    })
}

// 新闻内容数据
// const newsContents = reactive[IReleasePublish]

// 初始化加载
onMounted(async () => {
    releaseIds.push(props.releaseId)
    try {
        getTodoList(releaseIds);
    } catch (error) {
        console.error('获取数据失败:', error);
    }
});

// 处理添加按钮点击
const handleAddItem = () => {
    // 这里可以添加打开模态表单的逻辑
    console.log('添加按钮被点击')
}

const print = () => {
    window.print()
}
</script>

<style scoped>
/* 保持原有样式 */
.table td,
.table th {
    line-height: 45px;
}

/* 添加Tailwind样式覆盖 */
.indent-8 {
    text-indent: 2em;
}
</style>