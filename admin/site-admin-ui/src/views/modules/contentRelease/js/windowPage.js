// 可以封装成可配置的函数
function initHead(config) {
    // 创建meta标签
    const charsetMeta = document.createElement('meta');
    charsetMeta.setAttribute('charset', config.charset || 'utf-8');
    
    // 创建脚本
    config.scripts.forEach(script => {
        const s = document.createElement('script');
        s.src = script;
        document.head.appendChild(s);
    });

    // 创建样式
    const style = document.createElement('style');
    style.textContent = config.css || '';
    document.head.appendChild(style);
}

// 使用示例
initHead({
    charset: 'utf-8',
    scripts: [
        'https://cdn.tailwindcss.com',
        './tailwind.config.js'
    ],
    css: `
        .table td, .table th {
            line-height: 45px;
        }
    `
});