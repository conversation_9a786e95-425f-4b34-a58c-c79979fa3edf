<template>
    <div class="flex flex-col gap-2 h-full bg-[#f0f2f5]">
        <div class="p-2 bg-white border rounded">
            <el-form ref="elForm" :model="searchForm" class="flex flex-row flex-wrap gap-2 mt-3 ">
                <el-form-item label="标题" prop="title" class="text-xs">
                    <el-input v-model="searchForm.title" placeholder="请输入内容标题"></el-input>
                </el-form-item>
                <el-form-item label="发布日期" prop="daterange">
                    <el-date-picker v-model="daterange" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
                        value-format="YYYY-MM-DD" style="width:200px" />
                </el-form-item>

                <el-form-item label="分类标签" prop="tag">
                    <el-input v-model="searchForm.tag" placeholder="请输入"></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" icon="Search" @click="search">查询</el-button>
                    <el-button icon="RefreshRight" plain @click="clearForm">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="bg-white h-full shadow-md" v-loading="loading">
            <div class="flex gap-2 p-2 justify-between relative text-xs">
                <div>
                    <el-button icon="plus" type="primary" plain @click="edit('1')" class="iconclor">新增内容</el-button>
                    <el-button icon="plus" type="primary" plain @click="batchAudit" class="iconclor">批量发起审核</el-button>
                </div>
            </div>

            <div class="flex-1 relative">
                <div ref="table" class="absolute w-full">
                    <el-table :data="dataList" @selection-change="handleSelectionChange" style="width:100%"
                        class="text-xs" border>
                        <el-table-column type="selection" width="55"></el-table-column>
                        <el-table-column prop="columnName" label="所属栏目" align="center" width="150"
                            show-overflow-tooltip />
                        <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip />
                        <el-table-column prop="tagId" label="分类标签" align="center" show-overflow-tooltip />
                        <el-table-column prop="createDate" label="创建时间" align="center" width="140">
                        </el-table-column>
                        <!-- <el-table-column prop="releasePerson" label="审核人" align="center" width="100">
                            <template #default="{ row }">
                                {{ row.processInfo?.preNodeAuditComment?.operator }}
                            </template>
</el-table-column>
<el-table-column prop="taskName" label="流程状态" align="center" width="150">
    <template #default="{ row }">
                                {{ row.processInfo?.currentTaskName }}
                                <el-popover v-if="row.processInfo?.preNodeAuditComment?.auditInfo?.status == 'reject'"
                                    placement="bottom" title="退回理由" :width="200" trigger="click"
                                    :content="row.processInfo?.preNodeAuditComment?.auditInfo?.reason">
                                    <template #reference>
                                        <span class="text-blue-400 cursor-pointer"> (退回)</span>
                                    </template>
    </el-popover>
    </template>
</el-table-column> -->
                        <el-table-column label="操作" align="center" width="160px">
                            <template #default="scope">
                                <div class="flex justify-center flex-wrap">
                                    <el-button type="primary" class="px-2 !ml-0" text size="small"
                                        @click="audit(scope.row.id)">修改</el-button>
                                    <el-button type="primary" class="px-2" text size="small"
                                        @click="del([scope.row.id])">删除</el-button>
                                    <!-- <el-button type="primary" class="px-2 !ml-0" text size="small"
                                        @click="withdraw([scope.row.id])">撤回</el-button> -->

                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination :total="total" v-model:page="searchForm.pageNum" v-model:limit="searchForm.pageSize"
                        @pagination="search" />
                </div>


            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import ColumnTree from '@/components/ColumnTree/index.vue';
import { deleteReleaseByIds, getTodoList, withdrawRelease, submitAudit, auditRelease, checkReleaseIds } from '@/api/modules/release';
import { elConfirm, elError, elSuccess } from '@/utils/elmessage';
import { getReleaseType, getReleaseStatus, EReleaseType, EReleaseStatus } from '@/utils/release';
import useCurrentInstance from '@/utils/vueUtil/useCurrentInstance';
import { delConfirm } from '@/utils/common';
import { onActivated, reactive, ref, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import releaseIdsStore from '@/store/modules/releaseIds'
// @ts-ignore
import auth from '@/plugins/auth'

const emit = defineEmits(['total-change'])

const props = defineProps<{
    columnInfo: {
        columnId: string,
        columnIdList: string[]
    },
    tagList: ITag[]
}>()

watch(props.columnInfo, () => {
    searchForm.columnIdList = props.columnInfo?.columnIdList
})

const router = useRouter()

const { proxy } = useCurrentInstance()

const loading = ref(false)
const showBacthoper = ref(false)
const total = ref(0)

const daterange = ref<string[]>([])
const searchForm = reactive<Partial<IRelease>>({
    releaseDateStart: '',
    releaseDateEnd: '',
    columnIdList: [] as string[],
    pageNum: 1, pageSize: 10
})
const dataList = reactive<IRelease[]>([])
const search = () => {
    loading.value = true
    searchForm.releaseDateStart = daterange.value[0]
    searchForm.releaseDateEnd = daterange.value[1]
    getTodoList(searchForm).then(res => {
        dataList.length = 0
        total.value = res.total
        dataList.push(...res.rows)

    }).finally(() => {
        loading.value = false
        emit('total-change', total.value)
    })
}

const edit = (type: string, id?: string) => {
    router.push({ path: 'form', query: { id: id, columnId: props.columnInfo.columnId } })
}

onActivated(() => {
    search()
})

onMounted(() => {
    search();
});


const selectionList = reactive<IRelease[]>([])
const handleSelectionChange = (list: IRelease[]) => {
    selectionList.length = 0
    selectionList.push(...list)
}

const audit = (id: string) => {
    router.push({ path: 'form', query: { id: id } })
}

const withdraw = (ids: string[]) => {
    elConfirm({
        title: '撤回',
        message: '是否确认撤回?',
        then: () => {
            loading.value = true
            withdrawRelease(ids).then(() => {
                elSuccess('撤回成功')
                search()
            }).finally(() => {
                loading.value = false
            })
        }
    })
}

const submit = (releaseId: string) => {
    elConfirm({
        title: '审核',
        message: '是否确认提交审核?',
        then: () => {
            loading.value = true
            auditRelease({ releaseId: releaseId, status: 'approval', reason: '同意' }).then(() => {
                elSuccess('提交成功')
            }).finally(() => {
                loading.value = false
            })
        }
    })
}

const del = (ids: string[]) => {
    delConfirm(() => {
        deleteReleaseByIds(ids).then(() => {
            elSuccess('删除成功')
            search()
        })
    })
}


const clearForm = () => {
    proxy.resetForm('elForm');
    daterange.value.length = 0
    searchForm.columnIdList = []
    search()
}

const batchAudit = () => {
    if (selectionList.length > 0) {
        audits(selectionList.map(item => item.id!))
    }
}

const audits = (ids: string[]) => {
    releaseIdsStore().setIds(ids);
    checkReleaseIds(ids).then(res => {
        if (res.msg === 'success') {
            router.push({
                path: '/hdb/toAudit'
            })
        } else if (res.msg === 'false') {
            elError('选中的数据并非同一栏目下的内容，请重新选择!')
            // search()
        } else {
            elError('所选信息发布内容已存在审批单，请重新选择!')
        }
    })

}

defineExpose({
    search
})
</script>