<template>
    <div class="flex flex-col gap-2 h-full bg-[#f0f2f5]">
        <div class="bg-white h-full shadow-md" v-loading="loading">
            <div ref="table" class="w-full">
                <el-table :data="dataList" @selection-change="handleSelectionChange" style="width:100%" class="text-xs"
                    border>
                    <!-- <el-table-column type="selection" width="55"></el-table-column> -->
                    <el-table-column prop="nodeName" label="审核节点" align="center" width="150" show-overflow-tooltip />
                    <el-table-column prop="processName" label="审核人" align="center" show-overflow-tooltip />
                    <el-table-column prop="processTime" label="审核时间" align="center" show-overflow-tooltip />
                    <el-table-column prop="processMode" label="审核意见" align="center" width="80" />
                    <el-table-column prop="processComment" label="审核意见说明" align="center" width="300"
                        show-overflow-tooltip />
                </el-table>
                <pagination :total="total" v-model:page="searchForm.pageNum" v-model:limit="searchForm.pageSize"
                    @pagination="search" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { getReleasePublishLogList } from '@/api/modules/publish';
import Clipboard from '@/components/clipboard/index.vue';
import useCurrentInstance from '@/utils/vueUtil/useCurrentInstance';
import { delConfirm } from '@/utils/common';
import { onActivated, reactive, ref, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
// @ts-ignore
import auth from '@/plugins/auth'

const emit = defineEmits(['total-change'])

const props = defineProps<{
    releasedForm: string
}>()


const router = useRouter()

const { proxy } = useCurrentInstance()

const loading = ref(false)
const total = ref(0)

const daterange = ref<string[]>([])
const searchForm = reactive<Partial<IReleasePublishLog>>({
    releaseId: props.releasedForm,
    pageNum: 1, pageSize: 10
})
const dataList = reactive<IReleasePublishLog[]>([])
const search = () => {
    loading.value = true
    getReleasePublishLogList(searchForm).then(res => {
        dataList.length = 0
        total.value = res.total
        dataList.push(...res.rows)

    }).finally(() => {
        loading.value = false
        emit('total-change', total.value)
    })
}
onActivated(() => {
    search()
})

onMounted(() => {
    search();
});

const selectionList = reactive<IRelease[]>([])
const handleSelectionChange = (list: IRelease[]) => {
    selectionList.length = 0
    selectionList.push(...list)
}


const clearForm = () => {
    proxy.resetForm('elForm');
    daterange.value.length = 0
    search()
}

defineExpose({
    search
})
</script>
<style scoped>
.zdymenu .submenu {
    right: 50px;
    top: 8px;
    width: 75%;
}

.zdymenu:hover .submenu {
    opacity: 1;
    width: 75%;
}

:deep(.topClass) {
    font-weight: 600;
    background-color: #e5e9e8;
}
</style>