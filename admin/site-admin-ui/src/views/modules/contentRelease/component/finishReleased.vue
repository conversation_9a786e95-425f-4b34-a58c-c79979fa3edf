<template>
    <div class="flex flex-col gap-2 h-full bg-[#f0f2f5]">
        <div class="p-2 bg-white border rounded">
            <el-form ref="elForm" :model="searchForm" class="flex flex-row flex-wrap gap-2 mt-3 ">
                <el-form-item label="标题" prop="title" class="text-xs">
                    <el-input v-model="searchForm.title" placeholder="请输入内容标题"></el-input>
                </el-form-item>
                <el-form-item label="发布日期" prop="daterange">
                    <el-date-picker v-model="daterange" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
                        value-format="YYYY-MM-DD" style="width:200px" />
                </el-form-item>

                <el-form-item label="分类标签" prop="tag">
                    <el-input v-model="searchForm.tag" placeholder="请输入"></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" icon="Search" @click="search">查询</el-button>
                    <el-button icon="RefreshRight" plain @click="clearForm">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="bg-white h-full shadow-md" v-loading="loading">
            <div class="flex gap-2 p-2 justify-between relative text-xs">
                <div>
                    <!-- <el-button icon="plus" plain @click="batchShelves">批量取消发布</el-button> -->
                    <!-- <el-button icon="top" plain @click="batchToTop">批量置顶</el-button>
                    <el-button icon="bottom" plain @click="batchCancelToTop">批量取消置顶
                    </el-button> -->
                </div>

            </div>
            <div class="flex-1 relative">
                <div ref="table" class="absolute w-full">
                    <el-table :data="dataList" @selection-change="handleSelectionChange" :row-class-name="topTable"
                        style="width:100%" class="text-xs" border>
                        <el-table-column type="selection" width="55"></el-table-column>
                        <el-table-column prop="columnName" label="所属栏目" align="center" width="150"
                            show-overflow-tooltip />
                        <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip>
                            <template #default="{ row }">
                                <span style="color: red;">{{ row.mapperId == null ? '' : '[映射]' }}</span>{{ row.title }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="tag" label="分类标签" align="center" show-overflow-tooltip />
                        <el-table-column prop="releaseDate" label="发布时间" align="center" width="140" />
                        <el-table-column prop="releasePerson" label="发布人" align="center" width="100" />
                        <el-table-column label="操作" align="center" width="180px">
                            <template #default="scope">
                                <div class="flex justify-center flex-wrap">
                                    <el-button v-if="scope.row.mapperId == null" type="primary" class="px-2" text
                                        size="small" @click="edit(scope.row.releaseType, scope.row.id)">编辑</el-button>
                                    <el-button type="primary" class="px-2" text size="small"
                                        @click="getPublishLog(scope.row.id)">审批记录</el-button>
                                    <Clipboard :content="scope.row.id">
                                        <el-button type="primary" class="px-2" text size="small">复制链接</el-button>
                                    </Clipboard>
                                    <el-button type="primary" class="px-2" text size="small"
                                        @click="del([scope.row.id])">删除</el-button>

                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination :total="total" v-model:page="searchForm.pageNum" v-model:limit="searchForm.pageSize"
                        @pagination="search" />
                </div>
                <el-dialog title="审批列表" v-model="releasedDialog" width="50%" append-to-body destroy-on-close draggable>
                    <ReleasedLog :releasedForm="releasedForm" @close="releasedDialog = false;"></ReleasedLog>
                </el-dialog>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { deleteReleaseByIds, getReleasedList, batchReleaseToTop, batchReleasePublish, batchReleaseShelves, batchReleaseCancelToTop, listToDoRelease, getPublishIdRelease } from '@/api/modules/release';
import { elSuccess, elWarning } from '@/utils/elmessage';
import { getReleaseType, getReleaseStatus, EReleaseType, EReleaseStatus } from '@/utils/release';
import Clipboard from '@/components/clipboard/index.vue';
import useCurrentInstance from '@/utils/vueUtil/useCurrentInstance';
import { delConfirm } from '@/utils/common';
import ReleasedLog from './releasedLog.vue';
import { onActivated, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
// @ts-ignore
import auth from '@/plugins/auth'

const emit = defineEmits(['total-change'])

const props = defineProps<{
    columnInfo: {
        columnIdList: string[]
    },
    tagList: ITag[]
}>()

watch(props.columnInfo, () => {
    searchForm.columnIdList = props.columnInfo?.columnIdList
})

const router = useRouter()

const { proxy } = useCurrentInstance()

const loading = ref(false)
const total = ref(0)

const releasedDialog = ref(false)
const releasedForm = ref()

const daterange = ref<string[]>([])
const searchForm = reactive<Partial<IRelease>>({
    releaseDateStart: '',
    releaseDateEnd: '',
    columnIdList: [] as string[],
    pageNum: 1, pageSize: 10
})
const dataList = reactive<IRelease[]>([])
const search = () => {
    loading.value = true
    searchForm.releaseDateStart = daterange.value[0]
    searchForm.releaseDateEnd = daterange.value[1]
    getReleasedList(searchForm).then(res => {
        dataList.length = 0
        total.value = res.total
        dataList.push(...res.rows)

    }).finally(() => {
        loading.value = false
        emit('total-change', total.value)
    })
}
onActivated(() => {
    search()
})

const selectionList = reactive<IRelease[]>([])
const handleSelectionChange = (list: IRelease[]) => {
    selectionList.length = 0
    selectionList.push(...list)
}

const topTable = (row: any) => {
    if (row.row.isTop == 0) {
        return 'topClass'//给第2个td里面添加goodsInfo的类名
    } else {
        return ''
    }
}
const batchShelves = () => {
    const list = selectionList.filter(item => item.releaseStatus == EReleaseStatus.yfb)
    if (list.length > 0) {
        batchReleaseShelves(list.map(item => item.id!))
            .then(() => {
                elSuccess('取消发布成功')
                search()
            })
    } else {
        elSuccess('取消发布成功')
    }
}


const batchToTop = () => {
    const list = selectionList.filter(item => item.isTop != 0)
    if (list.length > 0) {
        batchReleaseToTop(list.map(item => item.id!))
            .then(() => {
                elSuccess('置顶成功')
                search()
            })
    } else {
        elSuccess('置顶成功')
    }
}

const batchCancelToTop = () => {
    const list = selectionList.filter(item => item.isTop == 0)
    if (list.length > 0) {
        batchReleaseCancelToTop(list.map(item => item.id!))
            .then(() => {
                elSuccess('取消置顶成功')
                search()
            })
    } else {
        elSuccess('取消置顶成功')
    }
}

const edit = (type: string, id?: string) => {
    router.push({ path: 'form', query: { id: id, columnId: searchForm.columnId } })
}

const getPublishLog = (id?: string) => {
    //查询是否存在审核申请，有则显示申请单信息，无则显示审批记录列表
    getPublishIdRelease(id).then(res => {
        if (res.msg != "") {
            router.push({ path: 'viewPublish', query: { id: res.msg } })
        } else {
            releasedForm.value = id;
            releasedDialog.value = true
        }
    })
}

const viewPublish = (id: string) => {
    router.push({ path: 'viewPublish', query: { id: id } })
}


const clearForm = () => {
    proxy.resetForm('elForm');
    daterange.value.length = 0
    searchForm.columnId = ''
    searchForm.columnIdList = []
    search()
}

const del = (ids: string[]) => {
    delConfirm(() => {
        deleteReleaseByIds(ids).then(() => {
            elSuccess('删除成功')
            search()
        })
    })
}

defineExpose({
    search
})
</script>
<style scoped>
.zdymenu .submenu {
    right: 50px;
    top: 8px;
    width: 75%;
}

.zdymenu:hover .submenu {
    opacity: 1;
    width: 75%;
}

:deep(.topClass) {
    font-weight: 600;
    background-color: #e5e9e8;
}
</style>