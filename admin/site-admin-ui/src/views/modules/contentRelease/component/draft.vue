<template>
    <div class="flex flex-col gap-2 h-full bg-[#f0f2f5]">
        <div class="p-2 bg-white border rounded">
            <el-form ref="elForm" :model="searchForm" class="flex flex-row flex-wrap gap-2 mt-3 ">
                <el-form-item label="标题" prop="title" class="text-xs">
                    <el-input v-model="searchForm.title" placeholder="请输入内容标题"></el-input>
                </el-form-item>
                <el-form-item label="发布日期" prop="daterange">
                    <el-date-picker v-model="daterange" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
                        value-format="YYYY-MM-DD" style="width:200px" />
                </el-form-item>

                <el-form-item label="分类标签" prop="tag">
                    <el-input v-model="searchForm.tag" placeholder="请输入"></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" icon="Search" @click="search">查询</el-button>
                    <el-button icon="RefreshRight" plain @click="clearForm">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="bg-white h-full shadow-md" v-loading="loading">
            <div class="flex gap-2 p-2 justify-between relative text-xs">
                <div>
                    <el-button icon="plus" type="primary" plain @click="edit('1')" class="iconclor">新增内容</el-button>
                    <el-button type="danger" icon="delete" plain @click="batchDel">批量删除</el-button>
                </div>

            </div>
            <div class="flex-1 relative">
                <div ref="table" class="absolute w-full">
                    <el-table :data="dataList" @selection-change="handleSelectionChange" style="width:100%"
                        class="text-xs" border>
                        <el-table-column type="selection" width="55"></el-table-column>
                        <el-table-column prop="columnName" label="所属栏目" align="center" width="150"
                            show-overflow-tooltip />
                        <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip />
                        <el-table-column prop="tag" label="分类标签" align="center" show-overflow-tooltip />
                        <el-table-column prop="createDate" label="创建时间" align="center" width="140" />
                        <el-table-column prop="createBy" label="创建人" align="center" width="100" />
                        <el-table-column label="操作" align="center" width="180px">
                            <template #default="scope">
                                <div class="flex justify-center flex-wrap">
                                    <el-button type="primary" class="px-2" text size="small"
                                        @click="edit(scope.row.releaseType, scope.row.id)">编辑</el-button>
                                    <!-- <el-button type="primary" class="px-2 !ml-0" text size="small"
                                        @click="submit([scope.row.id])">提交审核</el-button> -->
                                    <el-button type="primary" class="px-2 !ml-0" text size="small"
                                        @click="del([scope.row.id])">删除</el-button>

                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination :total="total" v-model:page="searchForm.pageNum" v-model:limit="searchForm.pageSize"
                        @pagination="search" />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { deleteReleaseByIds, getDraftList, batchReleaseToTop, batchReleasePublish, batchReleaseShelves, batchReleaseCancelToTop, submitAudit, auditRelease } from '@/api/modules/release';
import { elConfirm, elSuccess } from '@/utils/elmessage';
import { getReleaseType, getReleaseStatus, EReleaseType, EReleaseStatus } from '@/utils/release';
import Clipboard from '@/components/clipboard/index.vue';
import useCurrentInstance from '@/utils/vueUtil/useCurrentInstance';
import { delConfirm } from '@/utils/common';
import { onActivated, reactive, ref, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { dayjs } from 'element-plus';

// const { searchData, tagList } = searchTags()
const emit = defineEmits(['total-change'])

const props = defineProps<{
    columnInfo: {
        columnId: string
        columnIdList: string[]
    },
    tagList: ITag[]
}>()

watch(props.columnInfo, () => {
    searchForm.columnIdList = props.columnInfo?.columnIdList
})

const router = useRouter()

const { proxy } = useCurrentInstance()

const loading = ref(false)
const total = ref(0)

const daterange = ref<string[]>([])
const searchForm = reactive<Partial<IRelease>>({
    releaseDateStart: '',
    releaseDateEnd: '',
    columnIdList: [] as string[],
    pageNum: 1, pageSize: 10
})
const dataList = reactive<IRelease[]>([])
const search = () => {
    loading.value = true

    searchForm.releaseDateStart = daterange.value[0]
    searchForm.releaseDateEnd = daterange.value[1]
    getDraftList(searchForm).then(res => {
        dataList.length = 0
        total.value = res.total
        dataList.push(...res.rows)

    }).finally(() => {
        loading.value = false
        emit('total-change', total.value)
    })
}
onActivated(() => {
    search()
})

// onMounted(() => {
//     search();
// });


const selectionList = reactive<IRelease[]>([])
const handleSelectionChange = (list: IRelease[]) => {
    selectionList.length = 0
    selectionList.push(...list)
}

const batchDel = () => {
    if (selectionList.length > 0) {
        del(selectionList.map(item => item.id!))
    }
}


const edit = (type: string, id?: string) => {
    router.push({ path: 'form', query: { id: id, columnId: props.columnInfo.columnId } })
}

const submit = (releaseIds: string[]) => {
    elConfirm({
        title: '审核',
        message: '是否确认提交审核?',
        then: () => {
            submitAudit(releaseIds).then(() => {
                elSuccess('提交审核成功')
                search()
            })
        }
    })
}


const clearForm = () => {
    proxy.resetForm('elForm');
    daterange.value.length = 0
    searchForm.columnIdList = []
    search()
}

const del = (ids: string[]) => {
    delConfirm(() => {
        deleteReleaseByIds(ids).then(() => {
            elSuccess('删除成功')
            search()
        })
    })
}

const auditRelease = (ids: string[]) => {
    delConfirm(() => {
        deleteReleaseByIds(ids).then(() => {
            elSuccess('删除成功')
            search()
        })
    })
}

defineExpose({
    search
})
</script>