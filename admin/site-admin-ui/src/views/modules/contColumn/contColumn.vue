<template>
    <div class="p-2 flex flex-col gap-2 h-full bg-[#f0f2f5]">
        <div class="p-2 bg-white border rounded">
            <el-form ref="elForm" :model="searchForm" class="flex flex-row flex-wrap gap-2" label-width="80px">
                <el-form-item label="栏目名称" prop="columnName">
                    <el-input v-model="searchForm.columnName" placeholder="请输入栏目名称" clearable></el-input>
                </el-form-item>
                <el-form-item label="是否显示" prop="columnIsshow">
                    <el-select v-model="searchForm.columnIsshow" placeholder="请选择是否显示" clearable>
                        <el-option v-for="item in column_isshow_type" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="标签名称" prop="tagName">
                    <el-input v-model="searchForm.tagName" placeholder="请输入标签名称" clearable></el-input>
                </el-form-item>

                <el-form-item label-width="50px">
                    <el-button type="primary" icon="Search" @click="search">查询</el-button>
                    <el-button icon="RefreshRight" plain @click="reset">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="bg-white border rounded h-full" v-loading="loading">
            <div class="flex gap-2 p-2">
                <el-button type="primary" icon="plus" plain @click="editColumn({} as any)">添加</el-button>
            </div>
            <div ref="table" class=" w-full">
                <el-table :data="dataList" row-key="id" :tree-props="{ children: 'children' }" border style="width:100%"
                    class="text-xs">
                    <el-table-column prop="columnName" label="栏目名称" align="left" />
                    <el-table-column prop="planceNo" label="排序" align="center" width="100px" />
                    <el-table-column prop="columnPcUrl" label="链接" align="center" width="300px" />
                    <el-table-column label="是否审核" align="center">
                        <template #default="scope">
                            <dict-tag :options="sys_yes_no" :value="scope.row.needAudit" />
                        </template>
                    </el-table-column>
                    <el-table-column label="是否显示" align="center" width="260px">
                        <template #default="scope">
                            <dict-tag :options="column_isshow_type" :value="scope.row.columnIsshow" />
                        </template>

                    </el-table-column>
                    <el-table-column label="操作" align="center" width="260px">
                        <template #default="scope">
                            <el-button type="primary" text size="small" @click="editColumn(scope.row)">编辑</el-button>
                            <el-button class="!ml-0" size="small" type="primary" text
                                @click="addsubColumn(scope.row)">添加下级</el-button>
                            <el-button type="primary" text size="small" class="!ml-0"
                                @click="editTag(scope.row)">标签维护</el-button>
                            <el-button type="danger" text size="small" class="!ml-0"
                                @click="del(scope.row.id)">删除</el-button>
                            <!-- <div class="flex gap-1 text-gray-300">
                                <a class="text-blue-600 flex justify-center items-center" title="编辑"
                                    @click="editColumn(scope.row)">
                                    <el-icon>
                                        <Edit />
                                    </el-icon>
                                </a>|
                                <a v-if="scope.row.columnPid == '0'"
                                    class="text-blue-600 flex justify-center items-center" title="添加下级"
                                    @click="addsubColumn(scope.row)">
                                    <el-icon>
                                        <DocumentAdd />
                                    </el-icon>
                                </a>
                                <a v-else class="text-blue-600 flex justify-center items-center" title="标签维护"
                                    @click="editTag(scope.row)">
                                    <el-icon>
                                        <FolderOpened />
                                    </el-icon>
                                </a>|
                                <Clipboard class="m-auto" :content="getColumnLink(scope.row)">
                                    <a class="text-blue-600 flex justify-center items-center" title="复制链接">
                                        <el-icon>
                                            <Document-copy />
                                        </el-icon>
                                    </a>
                                </Clipboard>|
                                <a class="text-blue-600 flex justify-center items-center" title="删除"
                                    @click="del(scope.row.id)">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>
                                </a> -->
                            <!-- </div> -->
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-dialog title="栏目编辑" v-model="columnDialog" width="30%" append-to-body destroy-on-close draggable>
            <Form :columnList="columnList" :form="columnForm" @close="columnDialog = false; search()"></Form>
        </el-dialog>
        <el-dialog title="标签维护" v-model="tagDialog" width="30%" append-to-body destroy-on-close draggable>
            <ColumnTag :form="columnForm" @close="tagDialog = false; search()"></ColumnTag>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup name="ContColumn">
import Form from './form.vue';
import ColumnTag from './columnTag.vue';
import { deleteColumnByIds, getColumnList } from '@/api/modules/column';
import { elSuccess } from '@/utils/elmessage';
import { searchColumns } from './column';
import useCurrentInstance from '@/utils/vueUtil/useCurrentInstance';
import Clipboard from '@/components/clipboard/index.vue';
import { cloneDeep } from 'lodash';
import { delConfirm, filterColumnTree } from '@/utils/common';
import { getCurrentInstance, reactive, ref } from 'vue';

const { proxy } = useCurrentInstance();
const { column_isshow_type, column_link_type, system_module_type, sys_yes_no } = proxy.useDict("column_isshow_type", "system_module_type", "column_link_type", "sys_yes_no");
const searchForm = reactive<Partial<IColumn> & { tagName?: string }>({})
const dataList = reactive<IColumn[]>([])
const loading = ref(false)
let tempColumnList = reactive<IColumnTree[]>([]);
const { columnList, searchData } = searchColumns()
const search = () => {
    loading.value = true
    searchData().then(() => {
        dataList.length = 0
        dataList.push(...filterColumnTree(columnList, filterRule))
    }).finally(() => {
        loading.value = false
    })
}
search()

const filterRule = (column: IColumnTree) => {
    let columnNameState = searchForm.columnName ? column.columnName.includes(searchForm.columnName) ?? false : true
    let columnIsshowState = searchForm.columnIsshow ? column.columnIsshow?.includes(searchForm.columnIsshow ?? '') ?? false : true
    let tagState = searchForm.tagName ? column.tagList?.map(item => item.tagName).join(',').includes(searchForm.tagName ?? '') ?? false : true

    return columnNameState && columnIsshowState && tagState
}



const reset = () => {
    proxy.resetForm('elForm');
    search()
}

const getColumnLink = (column: IColumn) => {
    let basePath = `/pages/page`
    if (column.columnPid == '0') {
        basePath += `${column.retrieveType == '1' ? '/columnReleaseList' : '/columnDetail'}?`
    } else {
        basePath += `/columnReleaseList?`
    }
    if (column.columnPid == '0') {
        basePath += `columnId=${column.id}&`
    } else {
        basePath += `columnId=${column.columnPid}&children=${column.id}&`
    }
    basePath += `columnName=${column.columnName}`
    return basePath
}

const columnForm = ref<IColumnTree>({} as any)
const columnDialog = ref(false)
const editColumn = (column: IColumnTree) => {
    columnForm.value = column
    columnDialog.value = true
}

const tagDialog = ref(false)
const editTag = (column: IColumnTree) => {
    columnForm.value = column
    tagDialog.value = true
}

const addsubColumn = (column: IColumnTree) => {
    columnForm.value = {} as any
    columnForm.value.columnPid = column.id
    columnForm.value.columnLevel = column.columnLevel
    columnDialog.value = true
}

const del = (id: string) => {
    delConfirm(() => {
        deleteColumnByIds([id]).then(() => {
            elSuccess('删除成功')
            search()
        })
    })

}

</script>