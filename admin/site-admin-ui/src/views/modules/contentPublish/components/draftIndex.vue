<template>
    <div class="flex flex-col gap-2 h-full bg-[#f0f2f5]">
        <div class="p-2 bg-white border rounded">
            <el-form ref="elForm" :model="searchForm" class="flex flex-row flex-wrap gap-2 mt-3 ">
                <el-form-item label="标题" prop="title" class="text-xs">
                    <el-input v-model="searchForm.title" placeholder="请输入内容标题"></el-input>
                </el-form-item>
                <el-form-item label="发布日期" prop="daterange">
                    <el-date-picker v-model="daterange" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
                        value-format="YYYY-MM-DD" style="width:200px" />
                </el-form-item>

                <el-form-item label="所属栏目" prop="moduleSendName">
                    <el-input v-model="searchForm.moduleSendName" placeholder="请输入"></el-input>
                </el-form-item>

                <!-- <el-form-item label="流程状态" prop="status">
                    <el-select v-model="searchForm.status" placeholder="请选择流程状态" style="width: 150px;" >
                        <el-option label="草稿" value="1"></el-option>
                        <el-option label="待审核" value="2"></el-option>
                        <el-option label="待发布" value="3"></el-option>
                        <el-option label="已发布" value="4"></el-option>
                    </el-select>
                </el-form-item> -->

                <el-form-item>
                    <el-button type="primary" icon="Search" @click="search">查询</el-button>
                    <el-button icon="RefreshRight" plain @click="clearForm">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="bg-white h-full shadow-md" v-loading="loading">

            <div class="flex-1 relative">
                <div ref="table" class="absolute w-full">
                    <el-table :data="dataList" @selection-change="handleSelectionChange" style="width:100%"
                        class="text-xs" border>
                        <!-- <el-table-column type="selection" width="55"></el-table-column> -->
                        <el-table-column prop="moduleSendName" label="所属栏目" align="center" width="150"
                            show-overflow-tooltip />
                        <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip />
                        <el-table-column prop="status" label="流程状态" align="center">
                            <template #default="{ row }">
                                <div v-if="row.status == 1">草稿</div>
                                <div v-if="row.status == 2">待审核</div>
                                <div v-if="row.status == 3">待发布</div>
                                <div v-if="row.status == 4">已发布</div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="createDate" label="创建时间" align="center" width="140">
                            <template #default="{ row }">
                                {{ row.createDate }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="createByName" label="创建人" align="center" width="100">
                            <template #default="{ row }">
                                {{ row.createByName }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" width="160px">
                            <template #default="scope">
                                <div class="flex justify-center flex-wrap">
                                    <el-button type="primary" class="px-2 !ml-0" text size="small"
                                        @click="audit(scope.row.id)">编辑</el-button>
                                    <el-button type="primary" class="px-2 !ml-0" text size="small"
                                        @click="delPublish(scope.row.id)">删除</el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination :total="total" v-model:page="searchForm.pageNum" v-model:limit="searchForm.pageSize"
                        @pagination="search" />
                </div>


            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import ColumnTree from '@/components/ColumnTree/index.vue';
import { getPublishTodoList, getPublishDraftList, deletePublish } from '@/api/modules/publish';
import { elConfirm, elSuccess } from '@/utils/elmessage';
import { getReleaseType, getReleaseStatus, EReleaseType, EReleaseStatus } from '@/utils/release';
import useCurrentInstance from '@/utils/vueUtil/useCurrentInstance';
import { delConfirm } from '@/utils/common';
import { onActivated, reactive, ref, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
// @ts-ignore
import auth from '@/plugins/auth'

const emit = defineEmits(['total-change'])

const router = useRouter()

const { proxy } = useCurrentInstance()

const loading = ref(false)
const showBacthoper = ref(false)
const total = ref(0)

const daterange = ref<string[]>([])
const searchForm = reactive<Partial<IReleasePublish>>({
    applyTimeStart: '',
    applyTimeEnd: '',
    pageNum: 1, pageSize: 10
})
const dataList = reactive<IReleasePublish[]>([])
const search = () => {
    loading.value = true
    if (daterange == null || daterange.value == null) {
        searchForm.applyTimeStart = ''
        searchForm.applyTimeEnd = ''
    } else {
        searchForm.applyTimeStart = daterange.value[0]
        searchForm.applyTimeEnd = daterange.value[1]
    }
    getPublishDraftList(searchForm).then(res => {
        dataList.length = 0
        total.value = res.total
        dataList.push(...res.rows)
    }).finally(() => {
        loading.value = false
        emit('total-change', total.value)
    })
}
onActivated(() => {
    search()
})


const selectionList = reactive<IRelease[]>([])
const handleSelectionChange = (list: IRelease[]) => {
    selectionList.length = 0
    selectionList.push(...list)
}

const audit = (id: string) => {
    router.push({ path: 'toExamine', query: { id: id } })
}
const addAudit = (id: string) => {
    router.push({ path: 'toAudit' })
}


const delPublish = (id: string) => {
    elConfirm({
        title: '删除',
        message: '是否确认删除?',
        then: () => {
            loading.value = true
            deletePublish(id).then(() => {
                elSuccess('删除成功')
                search()
            }).finally(() => {
                loading.value = false
            })
        }
    })
}

const submit = (releaseId: string) => {
    elConfirm({
        title: '审核',
        message: '是否确认提交审核?',
        then: () => {
            loading.value = true
            auditRelease({ releaseId: releaseId, status: 'approval', reason: '同意' }).then(() => {
                elSuccess('提交成功')
            }).finally(() => {
                loading.value = false
            })
        }
    })
}


const clearForm = () => {
    proxy.resetForm('elForm');
    daterange.value.length = 0
    searchForm.columnIdList = []
    search()
}

// 在组件挂载后执行方法
onMounted(() => {
    search();
});

defineExpose({
    search
})
</script>