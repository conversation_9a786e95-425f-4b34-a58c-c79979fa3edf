<template>
    <div class="h-full">
        <div class="p-2 flex gap-2 h-full bg-[#f0f2f5]">
            <!-- <ColumnTree @node-clicked="handlerNodeClicked" @filter-column="handleFilter" class="w-60"></ColumnTree> -->
            <div class="flex-1">
                 <el-tabs v-model="activeTab" @tab-change="handleTabChange" type="border-card" class="h-full">
                    <el-tab-pane name="todo" label="待办">
                          <todoIndex ref="todoRef" :columnInfo="columnInfo" :tagList="tagList">
                          </todoIndex>
                    </el-tab-pane>
                    <el-tab-pane name="draft" label="草稿">
                          <draftIndex ref="draftRef" :columnInfo="columnInfo" :tagList="tagList">
                          </draftIndex>
                    </el-tab-pane>
                      <el-tab-pane name="all" label="全部">
                          <allIndex ref="allRef" :columnInfo="columnInfo" :tagList="tagList">
                          </allIndex>
                    </el-tab-pane>
                 </el-tabs>

            </div>
        </div>
    </div>
</template>

<script lang="ts" setup name="Release">
import ColumnTree from '@/components/ColumnTree/index.vue';
import { searchTags } from '../contColumn/column';
import { debounce } from 'lodash';
import { nextTick, onActivated, onMounted, reactive, ref } from 'vue';
import todoIndex from './components/todoIndex.vue'
import draftIndex from './components/draftIndex.vue'
import allIndex from './components/allIndex.vue'
import { filter, foreach, map } from 'tree-lodash';
import { getTodoList, getToReleaseList } from '@/api/modules/release';
// @ts-ignore
import auth from '@/plugins/auth'

const activeTab = ref('todo')

const todoRef = ref()
const draftRef = ref()
const allRef = ref()

const { searchData, tagList } = searchTags()
const columnInfo = ref<{
    columnId: string,
    columnIdList: string[]
}>({ columnIdList: [] } as any)
const handlerNodeClicked = debounce((column: IColumnTree) => {
    columnInfo.value.columnId = column.id
    columnInfo.value.columnIdList!.length = 0
    foreach(column as any, (item => {
        columnInfo.value.columnIdList.push(item.id)
    }))
    search()
    searchData(column.id)
}, 1000)

const handleFilter = debounce((columnIds: string[]) => {
    columnInfo.value.columnIdList!.length = 0
    columnInfo.value.columnIdList!.push(...columnIds)
    search()
}, 1000)

const handleTabChange = () => {
    search()
}

const search = () => {
    nextTick(() => {
        if (activeTab.value == 'todo') {
            toDoCount.value.search()
        }
    })

}

const toDoCount = ref(0)
// const getTodoCount = () => {
//     getTodoList({ pageNum: 1, pageSize: 0 }).then(res => toDoCount.value = res.total)
// }
const toReleaseCount = ref(0)
const getToReleaseCount = () => {
    getToReleaseList({ pageNum: 1, pageSize: 0 }).then(res => toReleaseCount.value = res.total)
}
onMounted(() => {
    // getTodoCount()
    getToReleaseCount()
})

</script>
<style scoped>
:deep(.el-tabs__content) {
    height: calc(100% - 30px);
}

:deep(.el-tabs--border-card>.el-tabs__content) {
    padding: 0;
}
</style>