<template>
  <div v-loading="loading">
    <el-page-header @back="back" :icon="ArrowLeft" class="p-3 bg-white border-b absolute w-full z-10 h-[96%]">
      <template #content>
        <span class="text-large font-600 mr-3"> 新增审核登记表 </span>
      </template>
      <el-scrollbar class="bg-white p-2">
        <el-tabs type="card" v-model="activeTabs">
          <el-tab-pane label="审批单" name="0">
            <div class="mb-10">

              <div class="mx-auto w-[700px] my-[15px]">
                <el-form ref="releaseFormRef" :model="auditForm">
                  <div class="container">
                    <h1 class="title">上海市核电办公室门户网站信息发布审核登记表({{ new Date().getFullYear() }})</h1>
                    <table class="review-table">
                      <tr>
                        <th rowspan="2" style="width: 25%;">栏目名称</th>
                        <th style="width: 25%;">一级栏目</th>
                        <th style="width: 25%;">二级栏目</th>
                        <th style="width: 25%;">三级栏目</th>
                      </tr>
                      <tr>
                        <td class="text-center">
                          {{ levelColumn.level1Name }}
                        </td>
                        <td class="text-center">
                          {{ levelColumn.level2Name }}
                        </td>
                        <td class="text-center">
                          {{ levelColumn.level3Name }}
                        </td>
                      </tr>
                      <tr>
                        <td class="text-center font-bold">序号</td>
                        <td colspan="4">
                          <div class="text-center relative">
                            <span class="font-bold">标题</span>
                            <!-- <el-button style="position: absolute; right: 0; top: 50%; transform: translateY(-50%)"
                              type="primary" text @click="addTitle">添加</el-button> -->
                          </div>
                        </td>
                      </tr>
                      <!-- <tr v-for="(item, index) in auditForm.title" :key="index">
                        <td class="text-center">{{index+1}}</td>
                        <td class="text-center" colspan="4">我国首款超长寿命碳-14核电池研制成功</td>
                      </tr> -->
                      <tr v-for="(item, id) in dataList" :key="id">
                        <td class="text-center">{{ id + 1 }}</td>
                        <td class="text-center" colspan="4">{{ item.title }}</td>
                      </tr>
                      <tr>
                        <td class="text-center font-bold">编辑</td>
                        <td class="text-center">{{ userStore.name }}</td>
                        <td class="text-center font-bold">审核</td>
                        <td class="text-center"> </td>
                      </tr>
                      <tr>
                        <td class="text-center font-bold">申请时间</td>
                        <td class="text-center"> </td>
                        <td class="text-center font-bold">发布时间</td>
                        <td class="text-center"> </td>
                      </tr>
                      <tr>
                        <td colspan="4">
                          <div>
                            <el-row>
                              <el-col class="text-center" :span="4">
                                <span>信息内容：</span>
                              </el-col>
                            </el-row>
                            <div style="margin-bottom: 15px" v-for="(item, id) in dataList" :key="id">
                              <el-row>
                                <el-col class="text-center"
                                  style="font-size: 18px;padding-left: 20px;padding-right: 20px; margin-top: 20px;font-weight: bold;line-height: 30px;"
                                  :span="24">
                                  <span>{{ item.title }}</span>
                                </el-col>
                                <el-col class="text-center" style="font-size: 12px;margin-top: 10px;" :span="24">
                                  <span>{{ item.source }}</span>
                                </el-col>
                                <!-- <el-col class="text-center" style="font-size: 14px;" :span="24">
                                  <span>{{ item.content }}</span>
                                </el-col> -->
                                <el-col class="text-center" style="font-size: 14px;padding: 20px; padding-top: 0;"
                                  :span="24">
                                  <span v-html="removeImages(item.content)"></span>
                                </el-col>
                              </el-row>
                            </div>

                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="4">
                          <div>
                            <el-row>
                              <el-col class="text-center font-bold add-margin-bottom" :span="5">
                                <span>部门负责人意见:</span>
                              </el-col>
                            </el-row>
                            <el-row>
                              <el-col class="text-center add-margin-bottom" :span="18">
                                <span></span>
                              </el-col>
                            </el-row>
                            <el-row>
                              <el-col class="text-center font-bold add-margin-bottom" :span="24">
                                <!-- <span style="font-size: 18px">同意</span> -->
                              </el-col>
                            </el-row>
                            <el-row>
                              <el-col class="text-center" :span="4" :offset="11">
                                <span>签名：</span>
                              </el-col>
                              <el-col class="text-center" :span="4" :offset="2">
                                <span>日期：</span>
                              </el-col>
                            </el-row>
                          </div>
                        </td>
                      </tr>
                    </table>
                  </div>
                </el-form>
              </div>
            </div>
          </el-tab-pane>
          <!-- <el-tab-pane label="流程日志" name="1">Config</el-tab-pane> -->
        </el-tabs>
      </el-scrollbar>
    </el-page-header>
    <div v-if="activeTabs === '0'" class="absolute bottom-0 w-full bg-gray-50 p-2 border-t z-10 flex justify-center">
      <el-button style="background-color: #67c23a;color: white;" @click="savePublish">保存草稿</el-button>
      <el-button type="primary" @click="submitPublish">提交审核</el-button>
    </div>
  </div>

</template>

<script lang="ts" setup>
// @ts-ignore
import useUserStore from '@/store/modules/user';
import { getReleaselistByIds } from '@/api/modules/release';
import { insertContentReleasePublishVo, submitContentReleasePublishVo } from '@/api/modules/publish';
import { getContentLevelColumn } from '@/api/modules/column';
// import WindowForm from './windowPage.vue';
import { ArrowLeft } from '@element-plus/icons';
import { dayjs, FormInstance, ElMessageBox } from 'element-plus';
import { elConfirm, elSuccess } from '@/utils/elmessage';
import { onMounted, computed, reactive, ref, watch } from 'vue';
import { searchColumns } from '../contColumn/column';
import { useRoute, useRouter } from 'vue-router';
import { filterColumnTree } from '@/utils/common';
import { showInReleaseModules } from '@/utils/release';
import releaseIdsStore from '@/store/modules/releaseIds'
import useTagsViewStore from "@/store/modules/tagsView";
import TinyMce from '@/components/TinyMce/index.vue'
// @ts-ignore
import auth from '@/plugins/auth'

// const {searchData: searchColumn, columnList} = searchColumns()

const userStore = useUserStore()
// const baseForm = ref()
const route = useRoute()
const router = useRouter()
const type = route.query.type as unknown as string
const loading = ref(false)
const disabled = ref(false)
const auditForm = ref({
  title: []
} as any)
const activeTabs = ref('0')

const columnId = ref()
const options: string[] = [];

const addTitle = () => {
  auditForm.value.title.push({
  })
}

// 退回
const back = () => {
  router.back()
}

// 打印功能
const handlePrint = () => {
  window.print();
};

const removeImages = (content) => {
  // 使用正则表达式匹配并移除所有 <img> 标签
  return content.replace(/<img[^>]*>/gi, '');
}

const dataList = reactive<IRelease[]>([])
const levelColumn = reactive<IlevelColumn>({
} as IlevelColumn)
const releasePublish = reactive<IReleasePublish>({} as IReleasePublish)

let thisColumnId = "";
let thisTitle = "";
let thisReleaseIds: []

const search = () => {
  loading.value = true
  thisReleaseIds = releaseIdsStore().getIds();
  getReleaselistByIds(thisReleaseIds).then(res => {
    dataList.push(...res.rows)
    if (dataList.length > 0) {
      thisColumnId = dataList[0].columnId
      getContentLevelColumn(thisColumnId).then(res2 => {
        levelColumn.level1Id = res2.data.level1Id
        levelColumn.level1Name = res2.data.level1Name
        levelColumn.level2Id = res2.data.level2Id
        levelColumn.level2Name = res2.data.level2Name
        levelColumn.level3Id = res2.data.level3Id
        levelColumn.level3Name = res2.data.level3Name
      }).finally(() => {
        loading.value = false
      })
    }
  }).finally(() => {
    loading.value = false
  })
}
onMounted(() => {
  search()
})
const currentTag = router.currentRoute.value
const tagsViewStore = useTagsViewStore()
const savePublish = (id: string) => {
  ElMessageBox.confirm('是否保存申请单', '提示', {
    type: 'warning',
  }).then(() => {
    releasePublish.columnId = thisColumnId
    releasePublish.releaseIds = thisReleaseIds
    releasePublish.createBy = userStore.id
    insertContentReleasePublishVo(releasePublish).then(() => {
      elSuccess('保存成功')
      tagsViewStore.delView(currentTag)
    }).finally(() => {
      router.push({
        path: '/hdb/published'
      })
    })
  })
}

const submitPublish = (id: string) => {
  ElMessageBox.confirm('是否提交审核流程？', '提示', {
    type: 'warning',
  }).then(() => {
    releasePublish.columnId = thisColumnId
    releasePublish.releaseIds = thisReleaseIds
    releasePublish.createBy = userStore.id
    submitContentReleasePublishVo(releasePublish).then(() => {
      elSuccess('提交成功')
      tagsViewStore.delView(currentTag)
    }).finally(() => {
      router.push({
        path: '/hdb/published'
      })
    })
  })
}


</script>

<style scoped>
:deep(.el-page-header__main) {
  height: 100%;
}

.container {
  padding: 20px;
  font-family: "Arial", sans-serif;
}

.title {
  font-size: 22px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
}

.review-table {
  width: 100%;
  border-collapse: collapse;
  /* text-align: center; */
  table-layout: fixed;
}

.review-table th,
.review-table td {
  border: 1px solid #333;
  padding: 10px;
}

.add-margin-bottom {
  margin-bottom: 11px;
}

.el-form-item {
  margin-bottom: 0;
}
</style>