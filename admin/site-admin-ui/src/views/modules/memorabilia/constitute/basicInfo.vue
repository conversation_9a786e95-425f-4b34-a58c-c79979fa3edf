<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="年份" prop="occurYear"> -->
      <!-- <el-input v-model="queryParams.occurYear" placeholder="请输入年份" clearable @keyup.enter="handleQuery" /> -->
      <!-- <el-date-picker v-model="queryParams.occurYear" type="year" value-format="YYYY" placeholder="请选择年度" clearable
            @keyup.enter="handleQuery" />
        </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="handleQuery">刷新</el-button>
        <el-button type="danger" @click="$router.back()">返回</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleUpdate"
          v-hasPermi="['memorabilia:memorabiliaInfo:add']">添加</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['memorabilia:memorabiliaInfo:remove']">删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="memorabiliaInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="大事记类型" align="center" prop="columnId">
        <template #default="scope">
          <dict-tag :options="memorabilia_info" :value="scope.row.columnId" />
        </template>
      </el-table-column>
      <el-table-column label="年份" align="center" prop="occurYear" />
      <el-table-column label="日期" align="center" prop="dates" />
      <el-table-column label="内容" align="center" prop="content" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['memorabilia:memorabiliaInfo:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['memorabilia:memorabiliaInfo:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改大事记输出对话框 -->
    <el-dialog title="编辑" v-model="open" width="600px" append-to-body destroy-on-close>
      <Suspense>
        <InfoForm :id="currentRow?.id" @close="cancel"></InfoForm>
      </Suspense>
    </el-dialog>

  </div>
</template>

<script setup>
import { dayjs } from 'element-plus';
import InfoForm from '../infoForm.vue';
import { listMemorabiliaInfo, xqList, getMemorabiliaInfo, delMemorabiliaInfo, addMemorabiliaInfo, updateMemorabiliaInfo } from "@/api/memorabilia/memorabiliaInfo";
import { useRoute } from 'vue-router';

const { proxy } = getCurrentInstance();

const { memorabilia_info } = proxy.useDict("memorabilia_info");

const memorabiliaInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const currentRow = ref()
const route = useRoute()
const occurTime = route.query.occurTime
const columnId = route.query.columnId

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    columnId: '',
    occurYear: '',
    dates: '',
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询大事记输出列表 */
function getList() {
  loading.value = true;
  let query = Object.assign({}, queryParams.value)
  query.columnId = columnId
  query.occurYear = dayjs(occurTime).format('YYYY')
  listMemorabiliaInfo(query).then(response => {
    memorabiliaInfoList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  getList();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  currentRow.value = row;
  open.value = true;
  // const _id = row.id || ids.value
  // getMemorabiliaInfo(_id).then(response => {
  //   form.value = response.data;
  //   open.value = true;
  //   title.value = "修改大事记输出";
  // });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除大事记的数据项？').then(function () {
    return delMemorabiliaInfo(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('memorabilia/memorabiliaInfo/export', {
    ...queryParams.value
  }, `memorabiliaInfo_${new Date().getTime()}.xlsx`)
}

getList();
</script>
