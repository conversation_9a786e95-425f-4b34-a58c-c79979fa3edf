<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="85px">
      <el-form-item label="大事记类型" prop="columnId">
        <!-- <el-input v-model="queryParams.columnId" placeholder="请输入栏目id" clearable @keyup.enter="handleQuery" /> -->
        <el-select v-model="queryParams.columnId" placeholder="请选择" clearable>
          <!-- <el-option label="全部" value="" /> -->
          <el-option v-for="item in memorabilia_info" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="大事记名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入大事记名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="发生时间" prop="occurTime">
        <el-date-picker clearable v-model="queryParams.occurTime" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择发生时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleUpdate"
          v-hasPermi="['memorabilia:memorabiliaBasic:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['memorabilia:memorabiliaBasic:remove']">删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['memorabilia:memorabiliaBasic:export']">导出</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport"
          v-hasPermi="['memorabilia:memorabiliaBasic:import']">导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain @click="$router.push({ path: '/hdb/memorabilia/file' })"
          v-hasPermi="['memorabilia:memorabiliaBasic:file']">附件管理</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="memorabiliaBasicList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="大事记类型" align="center" prop="columnId">
        <template #default="scope">
          <dict-tag :options="memorabilia_info" v-model:value="scope.row.columnId" />
        </template>
      </el-table-column>
      <el-table-column label="大事记名称" align="center" prop="name" />
      <el-table-column label="发生时间" align="center" prop="occurTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.occurTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="描述" align="center" prop="describes" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" text class="!text-main-blue !px-1"
            @click="$router.push({ path: '/hdb/memorabilia/basicInfo', query: { occurTime: scope.row.occurTime, columnId: scope.row.columnId } })">内容管理</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['memorabilia:memorabiliaBasic:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['memorabilia:memorabiliaBasic:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改大事记基础对话框 -->
    <el-dialog title="编辑" v-model="open" width="500px" append-to-body destroy-on-close>
      <Suspense>
        <BasicForm :id="currentRow?.id" @close="cancel"></BasicForm>
      </Suspense>
    </el-dialog>

    <!-- 大事记内容导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body destroy-on-close>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?columnType=' + upload.columnType" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.columnType" />是否是产业大事记的数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <!-- <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                     @click="importTemplate">下载模板</el-link> -->
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox, dayjs } from 'element-plus';
import { getToken } from "@/utils/auth";
import { listMemorabiliaBasic, getMemorabiliaBasic, delMemorabiliaBasic, addMemorabiliaBasic, updateMemorabiliaBasic } from "@/api/memorabilia/memorabiliaBasic";
import BasicForm from './basicForm.vue';

const { proxy } = getCurrentInstance();

const { memorabilia_info } = proxy.useDict("memorabilia_info");

const memorabiliaBasicList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const currentRow = ref()

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    columnId: null,
    name: null,
    occurTime: null,
    describes: null,
  },
  rules: {
  }
});

const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  columnType: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/memorabiliaInfo/impMemorabilia"
});

const { queryParams, form, rules } = toRefs(data);

/** 查询大事记基础列表 */
function getList() {
  loading.value = true;
  listMemorabiliaBasic(queryParams.value).then(response => {
    memorabiliaBasicList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  getList();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    columnId: null,
    name: null,
    occurTime: null,
    describes: null,
    delFlag: null,
    remark: null,
    createBy: null,
    createDate: null,
    updateBy: null,
    updateDate: null
  };
  proxy.resetForm("memorabiliaBasicRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  currentRow.value = row;
  open.value = true;
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除大事记基础数据项？').then(function () {
    return delMemorabiliaBasic(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
// function handleExport() {
//   proxy.download('memorabilia/memorabiliaBasic/export', {
//     ...queryParams.value
//   }, `memorabiliaBasic_${new Date().getTime()}.xlsx`)
// }

function handleImport() {
  upload.title = "大事记信息导入";
  upload.open = true;
}

const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
};

getList();
</script>
