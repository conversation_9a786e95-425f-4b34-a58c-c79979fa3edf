<template>
    <el-form ref="memorabiliaInfoRef" :model="form" :rules="rules" label-width="95px">
        <el-form-item label="大事记类型" prop="columnId">
            <el-select v-model="form.columnId" placeholder="请选择大事记类型">
                <el-option v-for="item in memorabilia_info" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <!-- <el-input v-model="form.columnId" placeholder="请输入栏目id" /> -->
        </el-form-item>
        <el-form-item label="年份" prop="occurYear">
            <el-input v-model="form.occurYear" placeholder="请输入年份" />
        </el-form-item>
        <el-form-item label="日期" prop="dates">
            <el-input v-model="form.dates" placeholder="请输入日期" />
        </el-form-item>
        <el-form-item label="内容">
            <el-input v-model="form.content" type="textarea" placeholder="请输入内容" :autosize="{ minRows: 3 }"
                resize="vertical" style="width: 500px;"></el-input>
        </el-form-item>
    </el-form>
    <div class="text-center">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="emit('close')">取 消</el-button>
    </div>

</template>

<script setup>
import { v4 as uuidv4 } from 'uuid'
import { getMemorabiliaInfo, addMemorabiliaInfo, updateMemorabiliaInfo } from "@/api/memorabilia/memorabiliaInfo";
import { ElMessage } from 'element-plus';

const emit = defineEmits(['close'])
const props = defineProps({
    id: String
})

const { proxy } = getCurrentInstance();
const { memorabilia_info } = proxy.useDict("memorabilia_info");

const form = reactive({
    id: uuidv4().replace(/-/g, '')
})
Object.assign(form, {})

if (props.id) {
    const res = await getMemorabiliaInfo(props.id)
    Object.assign(form, res.data)
}

const rules = reactive({
    columnId: [
        { required: true, message: "请选择大事记类型", trigger: "change" },
    ],
    occurYear: [
        { required: true, message: "请输入年份", trigger: "blur" },
    ],
    dates: [
        { required: true, message: "请输入日期", trigger: "blur" },
    ],
    content: [
        { required: true, message: "请输入内容", trigger: "blur" },
    ],
})


const memorabiliaInfoRef = ref()

/** 提交按钮 */
function submitForm() {
    // console.log(props.id, "props.id")
    // console.log(form.value, "form")
    memorabiliaInfoRef.value?.validate((valid) => {
        if (valid) {
            if (props.id != null) {
                updateMemorabiliaInfo(form).then(response => {
                    ElMessage.success("修改成功");
                    open.value = false;
                    emit('close')
                });
            } else {
                addMemorabiliaInfo(form).then(response => {
                    ElMessage.success("新增成功");
                    open.value = false;
                    emit('close')
                });
            }
        }
    });
}

</script>