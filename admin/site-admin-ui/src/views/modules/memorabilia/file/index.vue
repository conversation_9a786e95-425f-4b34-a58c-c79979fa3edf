<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="85px">
      <el-form-item label="大事记类型" prop="objId">
        <el-select v-model="queryParams.objId" placeholder="请选择" clearable>
          <!-- <el-option label="全部" value="" /> -->
          <el-option v-for="item in memorabilia_info" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['file:contentFile:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['file:contentFile:remove']">删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="contentFileList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="大事记类型" align="center" prop="objId">
        <template #default="scope">
          <dict-tag :options="memorabilia_info" v-model:value="scope.row.objId" />
        </template>
      </el-table-column>
      <el-table-column label="文件名称" align="center" prop="fileName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['file:contentFile:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改文件对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body destroy-on-close>
      <el-form ref="contentFileRef" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="大事记类型" prop="columnId">
          <el-select v-model="form.columnId" placeholder="请选择大事记类型">
            <el-option v-for="item in memorabilia_info" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="附件" class="!w-full">
          <TheUploader ref="uploadRef" :obj-id="form.id ? form.id : forms.id" accept="application/pdf"
            :before-upload="handleBeforeUpload">
            <template #tooltip>
              <span>请上传pdf文件</span>
            </template>

          </TheUploader>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { v4 as uuidv4 } from 'uuid'
import { listContentFile, getContentFile, delContentFile, addContentFile, updateContentFile } from "@/api/memorabilia/contentFile";
import TheUploader from '@/components/TheUploader.vue';
const { proxy } = getCurrentInstance();

const { memorabilia_info } = proxy.useDict("memorabilia_info");

const contentFileList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    objId: null,
    type: null,
    fileName: null,
    fileUrl: null
  },
  rules: {
  }
});

const forms = reactive({
  id: uuidv4().replace(/-/g, '')
})

const { queryParams, form, rules } = toRefs(data);

const uploadRef = ref()
const handleBeforeUpload = () => {
  const fileList = uploadRef.value.getFileList();
  if (fileList.length >= 3) {
    ElMessage.error('附件最多上传3个');
    return false;
  }
  return true
}

/** 查询文件列表 */
function getList() {
  loading.value = true;
  listContentFile(queryParams.value).then(response => {
    contentFileList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    objId: null,
    fileExt: null,
    fileSize: null,
    filePath: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null,
    delFlag: null,
    type: null,
    fileName: null,
    fileUrl: null,
    columnId: null
  };
  proxy.resetForm("contentFileRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加文件";
}

/** 提交按钮 */
function submitForm() {
  form.value.id = null ? form.value.id : forms.id
  proxy.$refs["contentFileRef"].validate(valid => {
    if (valid) {
      // if (form.value.id != null) {
      //   updateContentFile(form.value).then(response => {
      //     proxy.$modal.msgSuccess("修改成功");
      //     open.value = false;
      //     getList();
      //   });
      // } else {
      addContentFile(form.value).then(response => {
        proxy.$modal.msgSuccess("新增成功");
        open.value = false;
        getList();
      });
      // }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除文件编号为"' + _ids + '"的数据项？').then(function () {
    return delContentFile(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('file/contentFile/export', {
    ...queryParams.value
  }, `contentFile_${new Date().getTime()}.xlsx`)
}

getList();
</script>
