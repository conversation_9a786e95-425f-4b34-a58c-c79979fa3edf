<template>
    <el-form ref="memorabiliaBasicRef" :model="form" :rules="rules" label-width="85px">
        <el-form-item label="大事记类型" prop="columnId">
            <!-- <el-input v-model="form.columnId" placeholder="请输入栏目id" /> -->
            <el-select v-model="form.columnId" placeholder="请选择大事记类型">
                <el-option v-for="item in memorabilia_info" :label="item.label" :value="item.value"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="大事记名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入大事记名称" />
        </el-form-item>
        <el-form-item label="发生时间" prop="occurTime">
            <el-date-picker clearable v-model="form.occurTime" type="date" value-format="YYYY-MM-DD"
                placeholder="请选择发生时间">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="描述" prop="describes">
            <el-input v-model="form.describes" placeholder="请输入描述" />
        </el-form-item>
    </el-form>
    <div class="text-center">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="emit('close')">取 消</el-button>
    </div>

</template>

<script setup>
import { v4 as uuidv4 } from 'uuid'
import { getMemorabiliaBasic, addMemorabiliaBasic, updateMemorabiliaBasic } from "@/api/memorabilia/memorabiliaBasic";
import { ElMessage } from 'element-plus';

const emit = defineEmits(['close'])
const props = defineProps({
    id: String
})

const { proxy } = getCurrentInstance();
const { memorabilia_info } = proxy.useDict("memorabilia_info");

const form = reactive({
    id: uuidv4().replace(/-/g, '')
})

if (props.id) {
    const res = await getMemorabiliaBasic(props.id)
    Object.assign(form, res.data)
}


const memorabiliaBasicRef = ref()

/** 提交按钮 */
function submitForm() {
    memorabiliaBasicRef.value?.validate((valid) => {
        if (valid) {
            if (props.id != null) {
                updateMemorabiliaBasic(form).then(response => {
                    ElMessage.success("修改成功");
                    emit('close')
                });
            } else {
                addMemorabiliaBasic(form).then(response => {
                    ElMessage.success("新增成功");
                    emit('close')
                });
            }
        }
    });
}
</script>