<template>
    <div class="p-2 flex flex-col gap-2 h-full bg-[#f0f2f5]">
        <div class="px-2 bg-white border rounded">
            <el-form ref="elForm" :model="searchForm" class="flex flex-row flex-wrap gap-2 mt-4" label-width="80px">
                <el-form-item label="流程名称" prop="tbYear">
                    <el-input placeholder="请输入模板名称"></el-input>
                </el-form-item>
                <el-form-item label="流程状态" prop="tbType">
                    <el-select v-model="searchForm.tbType" placeholder="请选择流程状态">
                        <el-option label="未发布" value="0"></el-option>
                        <el-option label="已发布" value="1"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label-width="30px">
                    <el-button type="primary" icon="Search" @click="search">查询</el-button>
                    <el-button icon="RefreshRight" plain @click="proxy.resetForm('elForm'); search()">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="bg-white border rounded h-full" v-loading="loading">
            <div class="p-2">
                <el-button type="primary" plain icon="Plus" @click="editWorkflow">新增</el-button>
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
            </div>
            <div ref="table" class=" w-full">
                <el-table :data="dataList" style="width:100%" class="text-xs" border>
                    <el-table-column type="index" label="序号" align="center" width="55">
                        <template #default="{ row, $index }">
                            {{ (searchForm.pageNum - 1) * searchForm.pageSize + ($index + 1) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="tbYear" label="流程名称" align="center" width="150" />
                    <el-table-column prop="tbType" label="流程分组" align="center"></el-table-column>
                    <el-table-column prop="tbCount" label="流程状态" align="center" width="150" />
                    <el-table-column prop="address" label="操作" align="center" width="150">
                        <template #default="{ row }">
                            <!-- <div class=" gap-1 text-gray-300">
                                <el-button v-hasPermi="['enterprise:fill:manage:hz']" type="text" icon="Collection"
                                    @click="hz(row)">汇总</el-button>
                                <el-button v-hasPermi="['enterprise:fill:manage:mx']" type="text" icon="Document"
                                    @click="mx(row)">明细</el-button>
                            </div> -->
                        </template>
                    </el-table-column>
                </el-table>
                <pagination :total="total" v-model:page="searchForm.pageNum" v-model:limit="searchForm.pageSize"
                    @pagination="search" />
            </div>
        </div>
        <el-dialog v-model="workflowShow" title="Tips" fullscreen>

        </el-dialog>
    </div>
</template>

<script setup lang="js" name="Station">
import { getCurrentInstance, reactive, ref } from 'vue';


const { proxy } = getCurrentInstance();
const router = useRouter()

const fillFormShow = ref(false)
const total = ref(0)
const loading = ref(false)
const searchForm = reactive({ tbYear: '', tbType: '', pageNum: 1, pageSize: 10 })
const dataList = reactive([])
const search = () => {
    // loading.value = true
    // getTbLogTjList(searchForm).then(res => {
    //     dataList.length = 0
    //     dataList.push(...res.rows)
    //     total.value = res.total
    // }).finally(() => loading.value = false)
}
search()

const workflowShow = ref(false)
const editWorkflow = () => {
    workflowShow.value = true
}

</script>