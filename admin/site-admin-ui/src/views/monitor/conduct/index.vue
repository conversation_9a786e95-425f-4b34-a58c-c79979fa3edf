<template>
   <div class="app-container">
      <el-row>
      <el-col :span="24" class="card-box">
        <el-card>
          <template #header><Tickets style="width: 1em; height: 1em; vertical-align: middle;" /> <span style="vertical-align: middle;"  class="text-lg font-medium">今日概况</span></template>
          <div class="grid grid-cols-7 gap-10  text-base leading-[28px] justify-items-center mx-[40px] content-end">
             <div class="flex flex-col items-end">
                 
                 <span class="mt-[35px]">今日</span>
                 <span>昨日</span>
                 <span>与平均值比较</span>
             </div>
            <div class="border-l-0.1 border-solid border-gray-200"></div>
              <div class="flex flex-col">
                 <div class="font-semibold text-[#0079f6]">浏览量（PV）</div>
                 <span class="mt-[5px] font-semibold">620,745</span>
                 <span>1,548,545</span>
                 <span>148,545<svg t="1754893943520" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6319" width="14" height="14"><path d="M507.649902 1015.81158L160.153845 618.161453a21.75049 21.75049 0 0 1 16.376839-36.080224h187.565987a22.262266 22.262266 0 0 0 21.750489-22.262266 1630.774944 1630.774944 0 0 0-12.538517-207.525259C350.022825 182.895773 268.138628 87.705395 204.934264 38.574877A21.75049 21.75049 0 0 1 223.102321 0.447548C588.766434 60.837143 665.532868 470.514012 679.350826 563.401397a21.238713 21.238713 0 0 0 21.494601 18.679832h170.677372a21.75049 21.75049 0 0 1 16.120951 36.080224L540.403581 1015.81158a21.75049 21.75049 0 0 1-32.753679 0z" p-id="6320" fill="#1b952f"></path></svg></span>
             </div>
             <div class="border-l-0.1 border-solid border-gray-200"></div>
             <div class="flex flex-col">
                 <div class="font-semibold text-[#0079f6]">访客数（UV）</div>
                 <span class="mt-[5px] font-semibold">620,745</span>
                 <span>1,548,545</span>
                 <span>148,545<svg t="1754894029295" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7497" width="14" height="14"><path d="M529.143158 7.436293l347.212925 397.874796a21.748783 21.748783 0 0 1-16.375554 36.077393h-170.66398a21.492915 21.492915 0 0 0-21.492915 18.678367c-12.793402 92.624229-90.065549 502.268955-455.95684 562.909679a21.748783 21.748783 0 0 1-17.654895-38.380206c62.687669-49.126663 144.56544-144.309572 167.593564-313.694212a1571.285609 1571.285609 0 0 0 12.793402-207.508977 22.260519 22.260519 0 0 0-21.748783-22.260519H165.298811a21.748783 21.748783 0 0 1-16.375554-36.077393L496.39205 7.436293a21.748783 21.748783 0 0 1 32.751108 0z" p-id="7498" fill="#d81e06"></path></svg></span>
             </div>
             <div class="border-l-0.1 border-solid border-gray-200"></div>
             <div class="flex flex-col">
                 <div class="font-semibold text-[#0079f6]">IP数</div>
                 <span class="mt-[5px] font-semibold">325,545</span>
                 <span>58,841</span>
                 <span>48,545<svg t="1754894029295" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7497" width="14" height="14"><path d="M529.143158 7.436293l347.212925 397.874796a21.748783 21.748783 0 0 1-16.375554 36.077393h-170.66398a21.492915 21.492915 0 0 0-21.492915 18.678367c-12.793402 92.624229-90.065549 502.268955-455.95684 562.909679a21.748783 21.748783 0 0 1-17.654895-38.380206c62.687669-49.126663 144.56544-144.309572 167.593564-313.694212a1571.285609 1571.285609 0 0 0 12.793402-207.508977 22.260519 22.260519 0 0 0-21.748783-22.260519H165.298811a21.748783 21.748783 0 0 1-16.375554-36.077393L496.39205 7.436293a21.748783 21.748783 0 0 1 32.751108 0z" p-id="7498" fill="#d81e06"></path></svg></span>
             </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="24">
       <el-tabs v-model="activeName" class="demo-tabs mx-[15px] mt-[20px]" @tab-click="handleClick" type="card">
    <el-tab-pane label="今日" name="first" >
      <el-row>
      <el-col :span="12" class="card-box card-box1">
        <el-card>
          <template #header> <span style="vertical-align: middle; " class="text-lg font-medium">趋势分析</span>
            <el-button
              style="float: right; padding: 3px 0"
              link
              type="primary"
              icon="ArrowRightBold" @click="goToPage"
            ></el-button>
 <!-- <router-link :to="'/monitor/conduct-data'" >
                  <span>ddd</span>
               </router-link> -->
          
          </template>
            <div>
              <el-tabs v-model="activeName1" class="demo-tabs" @tab-click="handleClick1">
    <el-tab-pane label="浏览量（PV）" name="first1"><div ref="chartRef1" class="w-full h-[350px]"></div></el-tab-pane>
    <el-tab-pane label="访客数" name="second1">访客数</el-tab-pane>
    <el-tab-pane label="IP数" name="third1">IP数</el-tab-pane>

  </el-tabs>
            </div>
        </el-card>
      </el-col>
      <el-col :span="12" class="card-box pl-[15px] pr-0">
        <el-card>
          <template #header> <span style="vertical-align: middle; " class="text-lg font-medium">栏目Top5访问频率</span> <el-button
              style="float: right; padding: 3px 0"
              link
              type="primary"
              icon="ArrowRightBold" @click="goToPage_column"
            ></el-button></template>
            <div class="chart-container">
    <div ref="chartRef" class="chart"></div>
  </div>
        </el-card>
      </el-col>
      <el-col :span="12" class="card-box card-box1">
        <el-card>
          <template #header> <span style="vertical-align: middle; " class="text-lg font-medium">新闻浏览量排行</span>
            <el-button
              style="float: right; padding: 3px 0"
              link
              type="primary"
              icon="ArrowRightBold" @click="goToPage_news"
            ></el-button></template>
            <div class="flex justify-between text-sm"><span class="font-semibold">标题</span> <div class="flex"><span class="w-[90px] font-semibold">浏览量（PV）</span><span class="w-[90px] font-semibold">访客数（UV）</span></div></div>
            <div class="mt-[10px]">
                <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料海宾带队赴加宁新材料海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            </div> 
        </el-card>
        
      </el-col>
      <el-col :span="12" class="card-box pl-[15px] pr-0">
        <el-card>
          <template #header> <span style="vertical-align: middle; " class="text-lg font-medium">企业中心数据统计</span> <el-button
              style="float: right; padding: 3px 0"
              link
              type="primary"
              icon="ArrowRightBold" @click="goToPage_enterprise"
            ></el-button></template>
            <div class="flex justify-between text-sm"><span class="font-semibold">标题</span> <div class="flex"><span class="w-[90px] font-semibold">浏览量（PV）</span><span class="w-[90px] font-semibold">访客数（UV）</span></div></div>
            <div class="mt-[10px]">
                <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料海宾带队赴加宁新材料海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            <div class="flex justify-between text-sm mb-[10px]"><span class="line-clamp-1 w-[65%] cursor-pointer hover:text-[#3a82db]">聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研</span> <div class="flex"><span class="w-[80px]">10,569</span><span class="w-[80px]">9,589</span></div></div>
            </div>
        </el-card>
      </el-col>
      <el-col :span="12" class="card-box card-box1">
        <el-card>
          <template #header> <span style="vertical-align: middle; " class="text-lg font-medium">来源分析</span>
            <el-button
              style="float: right; padding: 3px 0"
              link
              type="primary"
              icon="ArrowRightBold"  @click="goToPage_source"
            ></el-button></template>
            <div class="relative">
              <div class="absolute left-[20px] top-[10px] z-[100]"><el-select v-model="value" placeholder="请选择来源分析" style="width:150px">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select></div>
            <div ref="chartRef2" style="width: 100%; height: 300px;"></div>
            </div>
        </el-card>
      </el-col>
      <el-col :span="12" class="card-box pl-[15px] pr-0">
        <el-card>
          <template #header> <span style="vertical-align: middle; " class="text-lg font-medium">地域分析</span> <el-button
              style="float: right; padding: 3px 0"
              link
              type="primary"
              icon="ArrowRightBold"
            ></el-button></template>
            <div class="h-[300px]">
              <img src="@/assets/images/map.png" alt=""  style="width: 400px;">
            </div>
        </el-card>
      </el-col>
   </el-row>
</el-tab-pane>
    <el-tab-pane label="昨日" name="second">Config</el-tab-pane>
    <el-tab-pane label="最近七日" name="third">Role</el-tab-pane>
    <el-tab-pane label="最近30日" name="fourth">Task</el-tab-pane>
  </el-tabs>
      </el-col>
      </el-row>
   </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router';
import type { TabsPaneContext } from 'element-plus'
import * as echarts from 'echarts';
const router = useRouter();

const goToPage = () => {
  router.push({ path: 'conduct/trend'})
};
const goToPage_column = () => {
  router.push({ path: 'conduct/column'})
};
const goToPage_source = () => {
  router.push({ path: 'conduct/source'})
};
const goToPage_news = () => {
  router.push({ path: 'conduct/news'})
};
const goToPage_enterprise = () => {
  router.push({ path: 'conduct/enterprise'})
};
const activeName = ref('first')
const activeName1 = ref('first1')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
const handleClick1 = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
const value = ref('')

const options = [
  {
    value: '浏览量(pv)',
    label: '浏览量(pv)',
  },
  {
    value: '访客数(uv)',
    label: '访客数(uv)',
  },
  {
    value: 'IP 数',
    label: 'IP 数',
  }
]
const chartRef1 = ref(null)
const chartRef = ref(null)
const chartRef2 = ref(null)
let chartInstance1 = null
let chartInstance = null

let chartInstance2 = null
onMounted(() => {
   initChart()
  window.addEventListener('resize', handleResize)
 
})




const handleResize = () => {
  chartInstance1 && chartInstance.resize()
}



const initChart = () => {
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: 20,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#eee'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: ['新闻中心', '系统党建', '产业发展', '核电科普', '机构职能'],
      inverse: true,
      axisLine: { show: false },
      axisTick: { show: false }
    },
    series: [{
      name: '访问频率',
      type: 'bar',
       barWidth: '25',
      data: [3570, 1228, 690, 644, 409],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: 'rgba(0, 121, 246, 0.8)' },
          { offset: 1, color: 'rgba(0, 121, 246,0.5)' }
        ]),
        borderRadius: [0, 4, 4, 0]
      },
      label: {
        show: true,
        position: 'right',
        formatter: '{c} 万'
      }
    }]
  }
  
  chartInstance.setOption(option)
   chartInstance1 = echarts.init(chartRef1.value)

  const option1 = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}'
    },
    xAxis: {
      type: 'category',
      data: ['0', '3', '6', '9', '12', '15', '18', '21'],
      axisLabel: {
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '数值'
    },
    series: [
       {
      name: '折线数据',
      type: 'line',
      data: [120, 200, 150, 80, 70, 110, 130],
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#74b5ff'  // 折线颜色
      },
      lineStyle: {
        width: 2
      }
    },
      {
      name: '示例数据',
      data: [1500, 2300, 2240, 2180, 1350, 1470, 2600],
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#1890FF'
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(24, 144, 255, 0.5)' },
          { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
        ])
      }
    }]
  }
  chartInstance2 = echarts.init(chartRef2.value)
  
  const option2 = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} 次 ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: '0%',
      data: ['直接访问', '搜索引擎', '外部链接'],
    
    },
    series: [
      {
        name: '电力消耗',
        type: 'pie',
        radius: ['35%', '60%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: '#fff',
          borderWidth: 2,
          color: function(params) {
            const colorList = [
              '#00c981',
              '#009ff9',
              '#00c9cc'
            ]
            return colorList[params.dataIndex]
          }
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{d}%',
          fontSize: 14
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: [
          { value: 1200, name: '直接访问' },
          { value: 800, name: '搜索引擎' },
          { value: 2000, name: '外部链接' }
        ]
      }
    ]
  }
  
  chartInstance2.setOption(option2)
  chartInstance1.setOption(option1)
}



onUnmounted(() => {
  if (chartInstance1) {
    chartInstance1.dispose()
     chartInstance1 = null
  }
    if (chartInstance) {
    chartInstance.dispose()
     chartInstance = null
  }
  if (chartInstance2) {
    chartInstance2.dispose()
     chartInstance2 = null
  }
   window.removeEventListener('resize', handleResize)
})
</script>
<style>
:deep(.el-tabs--card>.el-tabs__header .el-tabs__item.is-activ){background-color: #0079f6!important; color:#fff!important;}
:deep(.el-tabs--card>.el-tabs__header .el-tabs__item:first-child.is-activ){border-radius: 3px 0 0 3px;}
.el-tabs--card>.el-tabs__header .el-tabs__item.is-active{background-color: #0079f6!important; color:#fff!important;}
.chart-container {
  width: 100%;
  height: 100%;
}
.chart {
  width: 100%;
  height: 400px;
}
.card-box1{padding-left:0; padding-right:0;}
</style>