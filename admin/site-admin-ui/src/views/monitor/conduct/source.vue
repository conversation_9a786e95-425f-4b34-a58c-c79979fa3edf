<template>
   <div class="p-2 flex flex-col gap-2 bg-[#f0f2f5]">
        <div class="p-2 bg-white border rounded">
            <h1 class="mx-[20px] flex justify-between" ><span>来源分析</span> <el-button link
              type="primary" @click="goBack">返回 ></el-button></h1>
            <el-form  ref="" :inline="true" label-width="68px">
              <el-form-item label="时间" prop="demandTitle" style="width: 308px;">
                     <el-date-picker
        v-model="value1"
        type="daterange"
        range-separator="~"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :size="size"
      />
                </el-form-item>
                <el-form-item label="来源" prop="demandTitle">
                   <el-select v-model="value" placeholder="请选择来源" style="width:150px">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
                </el-form-item>
                 <el-form-item label="设备" prop="demandType">
                    <el-select v-model="value" placeholder="请选择设备" style="width:150px">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
                </el-form-item>
                <el-form-item>
            <el-button type="primary" icon="Search" >搜索</el-button>
            <el-button icon="Refresh" >重置</el-button>
         </el-form-item>
            </el-form>
        </div>
        <div class="p-2 bg-white border rounded flex flex-col relative">
           <div class="absolute left-[20px] top-[20px] z-[100]"><el-select v-model="value" placeholder="请选择" style="width:150px">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select></div>
          <div class="flex justify-between">
            <div class="w-40%"><div ref="chartRef" class="h-[350px] mt-[50px]"></div></div>
              
            <div class="w-56% "><div ref="chartRef2" class="h-[350px] mt-[50px]"></div></div>
          </div>
          </div>
     <div  class="p-2 bg-white border rounded">
       <el-table :data="tableData"  show-summary style="width: 100%">
        
    <el-table-column prop="column" label="来源" />
    <el-table-column prop="name" label="浏览量(IP)"/>
    <el-table-column prop="address" label="访客数(UV)" />
    <el-table-column prop="address" label="IP" />
  </el-table>
     </div>
   </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router';
import type { TabsPaneContext } from 'element-plus'
import * as echarts from 'echarts';
const router = useRouter();

const goBack = () => {
  // 跳转到指定路由路径
  router.back(); 
};

const value = ref('')

const options = [
  {
    value: '浏览量(pv)',
    label: '浏览量(pv)',
  },
  {
    value: '访客数(uv)',
    label: '访客数(uv)',
  },
  {
    value: 'IP 数',
    label: 'IP 数',
  }
]
const tableData = [
  {
    id: 1,
    column: '首页',
    name: '47',
    address:'47',
  },
  {
    id: 2,
   column: '机构职能',
    name: '445',
    address:'26098',
    children: [
      {
        id: 21,
        column:'机构概况',
        date: '111',
        name: '11',
        address: '12',
      },
      {
        id:22,
        column: '机构领导',
        name: '12',
        date: '111',
        address: '11',
      },]
  },
  {
     id: 3,
    column: '新闻中心',
    name: '47',
    address:'26098',
  },
  {
     id: 4,
    column: '系统党建',
    name: '47',
    address:'26098',
  },
]
const chartRef = ref(null)
let chartInstance = null
const chartRef2 = ref(null)
let chartInstance2 = null
onMounted(() => {
   initChart()
  window.addEventListener('resize', handleResize)
 
})

const handleResize = () => {
  chartInstance && chartInstance.resize()
  chartInstance2 && chartInstance2.resize()
}



const initChart = () => {
  chartInstance = echarts.init(chartRef.value)
  chartInstance2 = echarts.init(chartRef2.value)
   
 const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} 次 ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: '0%',
      data: ['搜索引擎', '直接访问', '外部链接'],
    
    },
     grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: 10,
      containLabel: true
    },
    series: [
      {
        name: '电力消耗',
        type: 'pie',
        radius: ['30%', '55%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 0,
          borderColor: '#fff',
          borderWidth: 1,
          color: function(params) {
            const colorList = [
              '#00c981',
              '#009ff9',
              '#00c9cc',
              '#ffd460',
              '#ff6e78',
              '#a062dd',
              '#4f55c8'
            ]
            return colorList[params.dataIndex]
          }
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{d}%',
          fontSize: 14
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: [
          { value: 1200, name: '搜索引擎' },
          { value: 800, name: '直接访问' },
          { value: 2000, name: '外部链接' }
        ]
      }
    ]
  }
  chartInstance.setOption(option)


  const option2 = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: [ '浏览量', '访客数', 'IP数'],
      top: '8%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['3', '6', '9', '12', '15', '18', '21'],
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '次',
        min: 0,
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '百分比(%)',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      
      {
        name: '浏览量',
        type: 'line',
        yAxisIndex: 0,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#FF9F43'
        },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#FF9F43'
        },
        data: [15000, 22000, 18000, 24000, 28000, 32000, 29000, 27000]
      },
      {
        name: '访客数',
        type: 'line',
        yAxisIndex: 0,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#28C76F'
        },
        symbol: 'diamond',
        symbolSize: 8,
        itemStyle: {
          color: '#28C76F'
        },
        data: [6000, 10000, 6000, 9000, 11000, 13000, 12000, 11000]
      },
      {
        name: 'IP数',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#EA5455'
        },
        symbol: 'triangle',
        symbolSize: 8,
        itemStyle: {
          color: '#EA5455'
        },
        data: [15, 18, 12, 17, 20, 22, 21, 19]
      }
    ]
  }
  
  chartInstance2.setOption(option2)
}





onUnmounted(() => {

    if (chartInstance) {
    chartInstance.dispose()
     chartInstance = null
  }
  if (chartInstance2) {
    chartInstance2.dispose()
     chartInstance2 = null
  }

   window.removeEventListener('resize', handleResize)
})
</script>
<style>
:deep(.el-tabs--card>.el-tabs__header .el-tabs__item.is-activ){background-color: #0079f6!important; color:#fff!important;}
:deep(.el-tabs--card>.el-tabs__header .el-tabs__item:first-child.is-activ){border-radius: 3px 0 0 3px;}
.el-tabs--card>.el-tabs__header .el-tabs__item.is-active{background-color: #0079f6!important; color:#fff!important;}
.chart-container {
  width: 100%;
  height: 100%;
}
.chart {
  width: 100%;
  height: 400px;
}
.card-box1{padding-left:0; padding-right:0;}
</style>