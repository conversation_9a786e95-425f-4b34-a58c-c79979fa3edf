<template>
  <div class="p-2 flex flex-col gap-2 bg-[#f0f2f5]">
    <div class="p-2 bg-white border rounded">
      <h1 class="mx-[10px] flex justify-between"><span>
          企业中心数据统计</span> <el-button link type="primary" @click="goBack">返回 ></el-button></h1>
      <el-form ref="" :inline="true" label-width="68px" class="ml-[15px]">
        <el-form-item label="企业名称" prop="demandTitle" style="width: 308px;">
          <el-input v-model="input" style="width: 240px" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="时间" prop="demandTitle" style="width: 308px;">
          <el-date-picker v-model="value1" type="daterange" range-separator="~" start-placeholder="开始时间"
            end-placeholder="结束时间" :size="size" />
        </el-form-item>
        <el-form-item label="来源" prop="demandTitle">
          <el-select v-model="value" placeholder="请选择来源" style="width:150px">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备" prop="demandType">
          <el-select v-model="value" placeholder="请选择设备" style="width:150px">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属栏目" prop="demandType">
          <el-select v-model="value" placeholder="请选择栏目" style="width:150px">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search">搜索</el-button>
          <el-button icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
   
    <div class="p-2 bg-white border rounded">
      <el-table :data="tableData" row-key="id" style="width: 100%">
        <el-table-column prop="name" label="企业名称" />
        <el-table-column prop="login" label="登录次数"  width="150"/>
        <el-table-column prop="look" label="总浏览量（PV）" width="150"/>
        <el-table-column prop="visit" label="浏览量占比" width="150"/>
     
      </el-table>
      <div class="flex justify-end mt-[5px] mb-[15px]"> <el-pagination
    size="small"
    background
    layout="prev, pager, next"
    :total="50"
    class="mt-4"
  /></div>
     
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router';

const router = useRouter();

const goBack = () => {
  // 跳转到指定路由路径
  router.back(); 
};

const value = ref('')

const options = [
  {
    value: '浏览量(pv)',
    label: '浏览量(pv)',
  },
  {
    value: '访客数(uv)',
    label: '访客数(uv)',
  },
  {
    value: 'IP 数',
    label: 'IP 数',
  }
]
const tableData = [
  {
    id: 1,
    name: '聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研',
    login: '2',
    look: '47',
    visit: '47',
    children: [
      {
        id: 11,
        name:'服务企业',
        login: '111',
        look: '47',
        visit: '47',
        children:[
          {id: 111,
            name:'企业直通车',
        login:'2',
        look: '47',
        visit: '47',
        } 
        ]
      }
      ]
  },
  {
    id: 2,
    name: '聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研',
    source: '中核智库',
    look: '47',
    visit: '47',
   
  },
  {
    id: 3,
    name: '聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研',
    source: '中核智库',
    look: '47',
    visit: '47',
   
  },
  {
    id: 4,
    name: '聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研',
    source: '中核智库',
    look: '47',
    visit: '47',
    column: '上海核电',
  },
  {
    id: 5,
    name: '聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研',
    source: '中核智库',
    look: '47',
    visit: '47',
  
  },
  {
    id: 6,
    name: '聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研',
    source: '中核智库',
    look: '47',
    visit: '47',
   
  },
  {
    id: 7,
    name: '聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研',
    source: '中核智库',
    look: '47',
    visit: '47',
   
  },
  {
    id: 8,
    name: '聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研',
    source: '中核智库',
    look: '47',
    visit: '47',
   
  },
  {
    id: 9,
    name: '聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研',
    source: '中核智库',
    look: '47',
    visit: '47',
 
  },
  {
    id: 10,
    name: '聚焦前沿“新”突破 锻造核电“硬”实力——陆海宾带队赴加宁新材料调研',
    source: '中核智库',
    look: '47',
    visit: '47',
   
  },
]

</script>
<style>
:deep(.el-tabs--card>.el-tabs__header .el-tabs__item.is-activ) {
  background-color: #0079f6 !important;
  color: #fff !important;
}

:deep(.el-tabs--card>.el-tabs__header .el-tabs__item:first-child.is-activ) {
  border-radius: 3px 0 0 3px;
}

.el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  background-color: #0079f6 !important;
  color: #fff !important;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.chart {
  width: 100%;
  height: 400px;
}

.card-box1 {
  padding-left: 0;
  padding-right: 0;
}
</style>