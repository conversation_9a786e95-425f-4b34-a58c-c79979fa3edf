<template>
   <div class="p-2 flex flex-col gap-2 bg-[#f0f2f5]">
        <div class="p-2 bg-white border rounded">
            <h1 class="mx-[20px] flex justify-between" ><span>趋势分析</span> <el-button link
              type="primary" @click="goBack">返回 ></el-button></h1>
            <el-form  ref="" :inline="true" label-width="68px">
              <el-form-item label="时间" prop="demandTitle" style="width: 308px;">
                     <el-date-picker
        v-model="value1"
        type="daterange"
        range-separator="~"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :size="size"
      />
                </el-form-item>
                <el-form-item label="来源" prop="demandTitle">
                   <el-select v-model="value" placeholder="请选择来源" style="width:150px">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
                </el-form-item>
                 <el-form-item label="设备" prop="demandType">
                    <el-select v-model="value" placeholder="请选择设备" style="width:150px">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
                </el-form-item>
                <el-form-item>
            <el-button type="primary" icon="Search" >搜索</el-button>
            <el-button icon="Refresh" >重置</el-button>
         </el-form-item>
            </el-form>
        </div>
        <div class="p-2 bg-white border rounded flex flex-col relative">
        <div class="grid grid-cols-3 gap-10  text-base leading-[28px] justify-items-center content-end mt-[10px]">
              <div class="flex flex-col">
                 <div class="font-semibold text-[#0079f6]">浏览量（PV）</div>
                 <span class="mt-[5px] font-semibold">620,745</span>
             </div>
            
             <div class="flex flex-col">
                 <div class="font-semibold text-[#0079f6]">访客数（UV）</div>
                 <span class="mt-[5px] font-semibold">620,745</span>
                
             </div>
           
             <div class="flex flex-col">
                 <div class="font-semibold text-[#0079f6]">IP数</div>
                 <span class="mt-[5px] font-semibold">325,545</span>
                
             </div>
          </div>
           <div class="absolute left-[20px] top-[90px] z-[100]"><el-select v-model="value" placeholder="请选择来源分析" style="width:150px">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select></div>
          <div ref="chartRef" class="w-full h-[350px] mt-[60px]"></div>
          </div>
     <div  class="p-2 bg-white border rounded">
       <el-table :data="tableData" show-summary style="width: 100%">
        
    <el-table-column prop="date" label="时间" />
    <el-table-column prop="name" label="浏览量(IP)"/>
    <el-table-column prop="address" label="访客数(UV)" />
    <el-table-column prop="address" label="IP" />
  </el-table>
     </div>
   </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router';
import * as echarts from 'echarts';
const router = useRouter();

const goBack = () => {
  // 跳转到指定路由路径
  router.back()
};

const value = ref('')

const options = [
  {
    value: '浏览量(pv)',
    label: '浏览量(pv)',
  },
  {
    value: '访客数(uv)',
    label: '访客数(uv)',
  },
  {
    value: 'IP 数',
    label: 'IP 数',
  }
]
const tableData = [
  {
    date: '00:01-00:02',
    name: '47',
    address:'47',
  },
  {
   date: '00:01-00:02',
    name: '445',
    address:'26098',
  },
  {
    date: '00:01-00:02',
    name: '47',
    address:'26098',
  },
  {
    date: '00:01-00:02',
    name: '47',
    address:'26098',
  },
]
const chartRef = ref(null)
let chartInstance = null

onMounted(() => {
   initChart()
  window.addEventListener('resize', handleResize)
 
})

const handleResize = () => {
  chartInstance && chartInstance.resize()
}



const initChart = () => {
  chartInstance = echarts.init(chartRef.value)
  
   
  const option = {
  grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: 30,
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}'
    },
    xAxis: {
      type: 'category',
      data: ['0', '3', '6', '9', '12', '15', '18', '21'],
      axisLabel: {
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '数值'
    },
    series: [
       {
      name: '折线数据',
      type: 'line',
      data: [120, 200, 150, 80, 70, 110, 130],
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#74b5ff'  // 折线颜色
      },
      lineStyle: {
        width: 2
      }
    },
      {
      name: '示例数据',
      data: [1500, 2300, 2240, 2180, 1350, 1470, 2600],
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#1890FF'
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(24, 144, 255, 0.5)' },
          { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
        ])
      }
    }]
  }
  
  chartInstance.setOption(option)
}



onUnmounted(() => {

    if (chartInstance) {
    chartInstance.dispose()
     chartInstance = null
  }

   window.removeEventListener('resize', handleResize)
})
</script>
<style>
:deep(.el-tabs--card>.el-tabs__header .el-tabs__item.is-activ){background-color: #0079f6!important; color:#fff!important;}
:deep(.el-tabs--card>.el-tabs__header .el-tabs__item:first-child.is-activ){border-radius: 3px 0 0 3px;}
.el-tabs--card>.el-tabs__header .el-tabs__item.is-active{background-color: #0079f6!important; color:#fff!important;}
.chart-container {
  width: 100%;
  height: 100%;
}
.chart {
  width: 100%;
  height: 400px;
}
.card-box1{padding-left:0; padding-right:0;}
</style>