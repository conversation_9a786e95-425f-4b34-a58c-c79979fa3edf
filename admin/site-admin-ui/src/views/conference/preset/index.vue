<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {listPreset, getPreset, savePreset, deletePreset, listModule} from "@/api/conference/module";
import TailwindColorClassPicker from "@/components/TailwindColorClassPicker/index.vue";
import {ElMessage} from "element-plus";
import ModuleDesigner from "@/views/conference/components/ModuleDesigner.vue";
import ConfModulePreviewDialog from "@/views/conference/components/ConfModulePreviewDialog.vue";

defineOptions({
  name: "ModulePreset",
})
const imgHost = import.meta.env.VITE_APP_BASE_API
const searchForm = ref<Partial<IConfModulePreset>>({})
const loading = ref(false)
const dataList = ref<IConfModulePreset[]>([])

const dialogFormRef = ref()
const dialogLoading = ref(false)
const dialogTitle = ref('新增预设')
const dialogVisible = ref(false)
const dialogForm = ref<Partial<IConfModulePreset>>({presetData:[]})
const rules = ref({
  presetName: [{ required: true, message: '请输入模块类型', trigger: 'blur' }],
})

// 预览
const previewDialogVisible = ref(false)
const previewData = ref<IConfPreviewData>({
  presetData: []
})

const search = async () => {
  loading.value = true
  try {
    const {data} = await listPreset(searchForm.value)
    dataList.value = data
  } finally {
    loading.value = false
  }
}
const reset = () => {
  searchForm.value = {}
  search()
}
const addPreset = async () => {
  dialogTitle.value = '新增预设'
  dialogVisible.value = true
}

const editPreset = async (id:number) => {
  dialogTitle.value = '编辑预设'
  dialogLoading.value = true
  dialogVisible.value = true
  try {
    const {data} = await getPreset(id)
    dialogForm.value = data
  } finally {
    dialogLoading.value = false
  }
}

const submitPreset = () => {
  if(!dialogForm.value.bannerColorClass && !dialogForm.value.bannerUrl){
    ElMessage.error('banner背景颜色和banner背景图至少设置一个')
    return
  }
  if(!dialogForm.value.backgroundColorClass && !dialogForm.value.backgroundUrl){
    ElMessage.error('背景颜色和背景图至少设置一个')
    return
  }
  dialogFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        dialogLoading.value = true
        await savePreset(dialogForm.value)
        dialogVisible.value = false
        await search()
      } finally {
        dialogLoading.value = false
      }
    }
  })
}


const delPreset = async (id: number) => {
  try {
    await deletePreset(id)
    await search()
  } catch (e) {
    console.log(e)
  }
}

const previewPreset = async (id: number) => {
  try {
    const {data} = await getPreset(id)
    previewData.value = data
    previewDialogVisible.value = true
  } catch (e) {
    console.log(e)
  }
}

const onClosed = () => {
  dialogForm.value = {presetData:[]}
  dialogFormRef.value.resetFields()
}
onMounted(() => {
  search()
})
</script>

<template>
  <div class="p-2 flex flex-col gap-2 h-full bg-[#f0f2f5]">
    <div class="p-2 bg-white border rounded">
      <el-form :model="searchForm" label-width="80px" inline>
        <el-form-item label="预设名称" prop="moduleType">
          <el-input v-model="searchForm.presetName" placeholder="请输入" clearable></el-input>
        </el-form-item>

        <el-form-item label-width="50px">
          <el-button type="primary" icon="Search" @click="search">查询</el-button>
          <el-button icon="RefreshRight" plain @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="bg-white border rounded h-full" v-loading="loading">
      <div class="flex gap-2 p-2">
        <el-button type="primary" icon="plus" plain @click="addPreset">添加</el-button>
      </div>
      <div ref="table" class=" w-full">
        <el-table :data="dataList" row-key="id" border class="text-xs w-full" stripe>
          <el-table-column prop="presetName" label="预设名称" align="center"/>
<!--          <el-table-column prop="bannerTitle" label="标题" align="center"/>-->
<!--          <el-table-column prop="bannerSubtitle" label="子标题" align="center"/>-->
          <el-table-column label="样例" align="center">
            <template #default="{row}">
              <el-button type="primary" text size="small" @click="previewPreset(row.id)">预览</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="260px">
            <template #default="{row}">
              <el-button type="primary" text size="small" @click="editPreset(row.id)">编辑</el-button>
              <el-popconfirm title="是否删除？" @confirm="delPreset(row.id)">
                <template #reference>
                  <el-button type="danger" text size="small" class="!ml-0">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="960px" @closed="onClosed" fullscreen>
      <div v-loading="dialogLoading">
        <el-form ref="dialogFormRef" :model="dialogForm" label-width="150px" :rules="rules">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="预设名称" prop="presetName">
                <el-input
                    v-model.trim="dialogForm.presetName"
                    placeholder="请输入预设名称"
                    clearable
                />
              </el-form-item>
              <el-form-item label="banner标题" prop="bannerTitle">
                <el-input
                    v-model="dialogForm.bannerTitle"
                    placeholder="请输入banner标题"
                    clearable
                />
              </el-form-item>
              <el-form-item label="banner子标题" prop="bannerSubtitle">
                <el-input
                    v-model="dialogForm.bannerSubtitle"
                    placeholder="请输入banner子标题"
                    clearable
                />
              </el-form-item>
              <el-form-item label="banner标题颜色" prop="bannerTitleColorClass">
                <tailwind-color-class-picker
                    v-model="dialogForm.bannerTitleColorClass"
                    type="text"
                />
              </el-form-item>
              <el-form-item label="banner子标题颜色" prop="bannerSubtitleColorClass">
                <tailwind-color-class-picker
                    v-model="dialogForm.bannerSubtitleColorClass"
                    type="text"
                />
              </el-form-item>
              <el-form-item label="banner背景图" prop="bannerUrl">
                <ImageUpload
                    v-model="dialogForm.bannerUrl"
                    :limit="1"
                    :fileSize="5"
                    :fileType="['png', 'jpg', 'jpeg']"
                    :isShowTip="true"
                />
              </el-form-item>
              <el-form-item label="banner背景颜色" prop="bannerColorClass">
                <tailwind-color-class-picker
                    v-model="dialogForm.bannerColorClass"
                    type="background"
                />
              </el-form-item>
              <el-form-item label="背景图" prop="backgroundUrl">
                <ImageUpload
                    v-model="dialogForm.backgroundUrl"
                    :limit="1"
                    :fileSize="5"
                    :fileType="['png', 'jpg', 'jpeg']"
                    :isShowTip="true"
                />
              </el-form-item>
              <el-form-item label="背景颜色" prop="backgroundColorClass">
                <tailwind-color-class-picker
                    v-model="dialogForm.backgroundColorClass"
                    type="background"
                />
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item prop="presetData">
                <module-designer v-model="dialogForm.presetData"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="text-center">
          <el-button type="primary" @click="submitPreset" :loading="dialogLoading">确 定</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </div>
    </el-dialog>

    <conf-module-preview-dialog v-model="previewDialogVisible" :preview-data="previewData"/>
  </div>
</template>

<style scoped>
.icon-input-wrapper {
  width: 100%;
}

.icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 32px;
  background-color: #f5f7fa;
  border-left: 1px solid #dcdfe6;
}

.preview-icon {
  font-size: 16px;
  color: #409eff;
}

.no-icon-text {
  font-size: 12px;
  color: #c0c4cc;
}

.icon-preview-large {
  margin-top: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.large-preview-icon {
  font-size: 24px;
  color: #409eff;
  min-width: 24px;
}

.icon-class-text {
  font-size: 12px;
  color: #909399;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background-color: #f1f3f4;
  padding: 2px 6px;
  border-radius: 3px;
}
</style>