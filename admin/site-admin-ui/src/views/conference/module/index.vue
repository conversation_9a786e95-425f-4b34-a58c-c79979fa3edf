<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {listModule, getModule, saveModule, deleteModule} from "@/api/conference/module";
import TailwindColorClassPicker from "@/components/TailwindColorClassPicker/index.vue";
import ModuleItem from "@/views/conference/components/ModuleItem.vue";
import {ElMessage} from "element-plus";
defineOptions({
  name: "ConfModule",
})
const imgHost = import.meta.env.VITE_APP_BASE_API
const searchForm = ref<Partial<IConfModule>>({})
const loading = ref(false)
const dataList = ref<IConfModule[]>([])
const dialogFormRef = ref()
const dialogLoading = ref(false)
const dialogTitle = ref('新增模块')
const dialogVisible = ref(false)
const dialogForm = ref<Partial<IConfModule>>({
  sort: 0
})
const rules = ref({
  moduleType: [{ required: true, message: '请输入模块类型', trigger: 'blur' }],
  path: [{ required: true, message: '请输入移动端跳转路由', trigger: 'blur' }],
  width: [{ required: true, message: '请选择宽度', trigger: 'change' }],
  height: [{ required: true, message: '请选择高度', trigger: 'change' }],
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  sort: [{ required: true, message: '请输入排序', trigger: 'change' }],
})

const search = async () => {
  loading.value = true
  try {
    const {data} = await listModule(searchForm.value)
    dataList.value = data
  } finally {
    loading.value = false
  }
}
const reset = () => {
  searchForm.value = {}
  search()
}
const addModule = () => {
  dialogTitle.value = '新增模块'
  dialogVisible.value = true
}

const editModule = async (id:number) => {
  dialogTitle.value = '编辑模块'
  dialogLoading.value = true
  dialogVisible.value = true
  try {
    const {data} = await getModule(id)
    dialogForm.value = data
  } finally {
    dialogLoading.value = false
  }
}

const submitModule = () => {
  if(!dialogForm.value.bgColorClass && !dialogForm.value.bgImgUrl){
    ElMessage.error('背景颜色和背景图至少设置一个')
    return
  }
  dialogFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        dialogLoading.value = true
        await saveModule(dialogForm.value)
        dialogVisible.value = false
        await search()
      } finally {
        dialogLoading.value = false
      }
    }
  })
}

const delModule = async (id: number) => {
  try {
    await deleteModule(id)
    await search()
  } catch (e) {
    console.log(e)
  }
}

const onClosed = () => {
  dialogForm.value = {
    sort: 0
  }
  dialogFormRef.value.resetFields()
}
onMounted(() => {
  search()
})
</script>

<template>
  <div class="p-2 flex flex-col gap-2 h-full bg-[#f0f2f5]">
    <div class="p-2 bg-white border rounded">
      <el-form :model="searchForm" label-width="80px" inline>
        <el-form-item label="模块类型" prop="moduleType">
          <el-input v-model="searchForm.moduleType" placeholder="请输入" clearable></el-input>
        </el-form-item>

        <el-form-item label-width="50px">
          <el-button type="primary" icon="Search" @click="search">查询</el-button>
          <el-button icon="RefreshRight" plain @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="bg-white border rounded h-full" v-loading="loading">
      <div class="flex gap-2 p-2">
        <el-button type="primary" icon="plus" plain @click="addModule">添加</el-button>
      </div>
      <div ref="table" class=" w-full">
        <el-table :data="dataList" row-key="id" border class="text-xs w-full" stripe>
          <el-table-column prop="moduleType" label="模块类型" align="left"/>
          <el-table-column prop="title" label="标题" align="center"/>
          <el-table-column prop="subtitle" label="子标题" align="center"/>
          <el-table-column prop="path" label="跳转路由" align="center"/>
          <el-table-column label="样例" header-align="center">
            <template #default="{row}">
              <module-item :module="row"/>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="260px">
            <template #default="{row}">
              <el-button type="primary" text size="small" @click="editModule(row.id)">编辑</el-button>
              <el-popconfirm title="是否删除？" @confirm="delModule(row.id)">
                <template #reference>
                  <el-button type="danger" text size="small" class="!ml-0">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="960px" @closed="onClosed" >
      <div v-loading="dialogLoading">
        <el-form ref="dialogFormRef" :model="dialogForm" label-width="120px" :rules="rules">
          <!-- 第一行：基础信息 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模块类型" prop="moduleType">
                <el-input
                    v-model.trim="dialogForm.moduleType"
                    placeholder="请输入模块类型"
                    clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="移动端跳转路由" prop="path">
                <el-input
                    v-model.trim="dialogForm.path"
                    placeholder="请输入"
                    clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行：尺寸配置 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="宽度" prop="width">
                <el-select v-model="dialogForm.width" placeholder="请选择宽度" style="width: 100%">
                  <el-option label="半宽" value="half"></el-option>
                  <el-option label="全宽" value="wide"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="高度" prop="height">
                <el-select v-model="dialogForm.height" placeholder="请选择高度" style="width: 100%">
                  <el-option label="标准高度" value="1x"></el-option>
                  <el-option label="双倍高度" value="2x"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第三行：标题信息 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="标题" prop="title">
                <el-input
                    v-model="dialogForm.title"
                    placeholder="请输入标题"
                    clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="子标题" prop="subtitle">
                <el-input
                    v-model="dialogForm.subtitle"
                    placeholder="请输入子标题"
                    clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第四行：样式配置 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="排序" prop="sort">
                <el-input-number
                    class="w-full"
                    v-model="dialogForm.sort"
                    placeholder="请输入排序"
                    step-strictly
                    :step="1"
                />
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="icon样式" prop="iconClass">
                <div class="icon-input-wrapper">
                  <el-input
                      v-model="dialogForm.iconClass"
                      placeholder="请输入icon样式类名"
                      clearable
                  >
                  </el-input>
                  <div class="icon-preview-large" v-if="dialogForm.iconClass">
                    <div class="preview-label">图标预览：</div>
                    <i :class="dialogForm.iconClass" class="large-preview-icon"></i>
                    <span class="icon-class-text">{{ dialogForm.iconClass }}</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第五行：颜色配置 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="标题颜色" prop="textColorClass">
                <tailwind-color-class-picker
                    v-model="dialogForm.textColorClass"
                    type="text"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="背景颜色" prop="bgColorClass">
                <tailwind-color-class-picker
                    v-model="dialogForm.bgColorClass"
                    type="background"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第六行：背景图上传 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="背景图" prop="bgImgUrl">
                <ImageUpload
                    v-model="dialogForm.bgImgUrl"
                    :limit="1"
                    :fileSize="5"
                    :fileType="['png', 'jpg', 'jpeg']"
                    :isShowTip="true"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="text-center">
          <el-button type="primary" @click="submitModule" :loading="dialogLoading">确 定</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.icon-input-wrapper {
  width: 100%;
}

.icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 32px;
  background-color: #f5f7fa;
  border-left: 1px solid #dcdfe6;
}

.preview-icon {
  font-size: 16px;
  color: #409eff;
}

.no-icon-text {
  font-size: 12px;
  color: #c0c4cc;
}

.icon-preview-large {
  margin-top: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.large-preview-icon {
  font-size: 24px;
  color: #409eff;
  min-width: 24px;
}

.icon-class-text {
  font-size: 12px;
  color: #909399;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background-color: #f1f3f4;
  padding: 2px 6px;
  border-radius: 3px;
}
</style>