<script setup lang="ts">

import {useRouter} from "vue-router";
import {ref, onMounted} from "vue";
import {pageConfBasic} from "@/api/conference/basic";
import ModuleBanner from "@/views/conference/components/ModuleBanner.vue";

defineOptions({
  name: "ConfAttachment",
})

const router = useRouter();
const searchForm = ref<IPageQuery<Partial<IConfBasic>>>({
  pageNum: 1,
  pageSize: 10,
  status: '1'
})
const loading = ref(false)
const dataList = ref<IConfBasic[]>([])
const total = ref(0);

const search = async () => {
  loading.value = true
  try {
    const data = await pageConfBasic(searchForm.value)
    total.value = data.total
    dataList.value = data.rows
  } finally {
    loading.value = false
  }
}
const reset = () => {
  searchForm.value = {
    pageNum: 1,
    pageSize: 10,
  }
  search()
}

const toDetail = (row: IConfBasic) => {
  router.push({
    name: 'ConfAttachmentDetail',
    query: {
      confId: row.id
    }
  })
}

onMounted(() => {
  search()
})
</script>

<template>
  <div class="p-2 flex flex-col gap-2 h-full bg-[#f0f2f5]">
    <div class="p-2 bg-white border rounded">
      <el-form :model="searchForm" label-width="80px" inline>
        <el-form-item label="会务名称" prop="name">
          <el-input v-model="searchForm.name" placeholder="请输入" clearable></el-input>
        </el-form-item>

        <el-form-item label-width="50px">
          <el-button type="primary" icon="Search" @click="search">查询</el-button>
          <el-button icon="RefreshRight" plain @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="bg-white border rounded h-full p-4" v-loading="loading">
      <el-row :gutter="20">
        <el-col v-for="item in dataList" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
          <el-card class="cursor-pointer" shadow="hover" @click="toDetail(item)">
            <template #header>
              <div class="flex items-center justify-between">
                <span>{{ item.name }}</span>
              </div>
            </template>
            <div class="h-[250px]">
              <module-banner :preview-data="item"/>
            </div>
          </el-card>
        </el-col>
        <el-col v-if="dataList.length === 0">
          <el-empty description="暂时没有发布的会议"/>
        </el-col>
      </el-row>
      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="searchForm.pageNum"
          v-model:limit="searchForm.pageSize"
          @pagination="search"
      />
    </div>
  </div>
</template>

<style scoped>

</style>