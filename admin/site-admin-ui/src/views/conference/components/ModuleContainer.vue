<script setup lang="ts">
import {PropType} from "vue";
import ModuleItem from "@/views/conference/components/ModuleItem.vue";

const props = defineProps({
  modules: {
    type: Array as PropType<IConfModuleData[]>,
    required: true
  }
})
</script>

<template>
    <div class="grid grid-cols-2 auto-rows-min gap-3">
      <module-item v-for="module in modules" :key="module.moduleType" :module="module"/>
    </div>
</template>

<style scoped>

</style>