<script setup lang="ts">
import {computed, type CSSProperties, PropType} from "vue";
import ModuleContainer from "@/views/conference/components/ModuleContainer.vue";
import ModuleBanner from "@/views/conference/components/ModuleBanner.vue";
const imgHost = import.meta.env.VITE_APP_BASE_API

const props = defineProps({
  previewData: {
    type: Object as PropType<IConfPreviewData>,
    required: true
  }
})

// const getBannerBackgroundClass = (previewData: IConfPreviewData) => {
//   return previewData.bannerUrl ? '' : previewData.bannerColorClass;
// };
//
// const getBannerTitleClass = (previewData: IConfPreviewData) => {
//   return previewData.bannerTitleColorClass;
// };
//
// const getBannerSubtitleClass = (previewData: IConfPreviewData) => {
//   return previewData.bannerSubtitleColorClass;
// };

const getBackgroundClass = (previewData: IConfPreviewData) => {
  return previewData.backgroundUrl ? '' : previewData.backgroundColorClass;
};

const getBackgroundStyle = (previewData: IConfPreviewData): CSSProperties => {
  if (previewData.backgroundUrl) {
    return {
      backgroundImage: `url(${imgHost}${previewData.backgroundUrl})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center bottom',
      backgroundRepeat: 'no-repeat'
    };
  }
  return {};
};
</script>

<template>
  <!-- 模块内容区域 -->
  <div class="modules-container flex flex-col">
    <!-- Banner区域 - 使用img标签 -->
<!--    <div class="banner-container relative overflow-hidden">
      &lt;!&ndash; 背景图片 &ndash;&gt;
      <img
          v-if="previewData.bannerUrl"
          :src="`${imgHost}${previewData.bannerUrl}`"
          alt="banner背景"
          class="w-full h-auto object-cover"
      />
      &lt;!&ndash; 没有背景图时的颜色背景 &ndash;&gt;
      <div
          v-else
          class="w-full h-[248px]"
          :class="getBannerBackgroundClass(previewData)">
      </div>

      &lt;!&ndash; 文字内容覆盖层 &ndash;&gt;
      <div class="absolute inset-0 flex flex-col justify-center text-center">
        <div class="text-2xl p-2 font-bold" :class="getBannerTitleClass(previewData)">{{previewData.bannerTitle}}</div>
        <div class="text-lg p-2 font-bold" :class="getBannerSubtitleClass(previewData)">{{previewData.bannerSubtitle}}</div>
      </div>
    </div>-->
    <module-banner :preview-data="previewData"/>

    <div v-if="previewData.presetData.length === 0" class="empty-preview">
      <div class="i-ic:round-dashboard mx-auto mb-4 w-16 h-16 text-gray-400"></div>
      <p class="text-sm text-gray-500">暂无模块内容</p>
    </div>
    <div v-else class="p-4 flex-1"
         :class="getBackgroundClass(previewData)"
         :style="getBackgroundStyle(previewData)">
      <module-container  :modules="previewData.presetData"/>
    </div>
  </div>
</template>

<style scoped>

.modules-container {
  background: #f8f9fa;
  min-height: 100vh;
  overflow-y: auto;
  width: 100%;
  max-width: 460px;
}

.banner-container {
  box-sizing: border-box;
  max-width: 460px;
}

.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 20px;
}
</style>