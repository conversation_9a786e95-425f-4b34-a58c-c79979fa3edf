<script setup lang="ts">
import {type CSSProperties, PropType} from "vue";
const imgHost = import.meta.env.VITE_APP_BASE_API

const props = defineProps({
  previewData: {
    type: Object as PropType<IConfPreviewData>,
    required: true
  }
})

const getBannerBackgroundClass = (previewData: IConfPreviewData) => {
  return previewData.bannerUrl ? '' : previewData.bannerColorClass;
};

const getBannerTitleClass = (previewData: IConfPreviewData) => {
  return previewData.bannerTitleColorClass;
};

const getBannerSubtitleClass = (previewData: IConfPreviewData) => {
  return previewData.bannerSubtitleColorClass;
};

</script>

<template>
  <div class="banner-container relative overflow-hidden">
    <!-- 背景图片 -->
    <img
        v-if="previewData.bannerUrl"
        :src="`${imgHost}${previewData.bannerUrl}`"
        alt="banner背景"
        class="w-full h-auto object-cover"
    />
    <!-- 没有背景图时的颜色背景 -->
    <div
        v-else
        class="w-full h-[248px]"
        :class="getBannerBackgroundClass(previewData)">
    </div>

    <!-- 文字内容覆盖层 -->
    <div class="absolute inset-0 flex flex-col justify-center text-center">
      <div class="text-2xl p-2 font-bold" :class="getBannerTitleClass(previewData)">{{previewData.bannerTitle}}</div>
      <div class="text-lg p-2 font-bold" :class="getBannerSubtitleClass(previewData)">{{previewData.bannerSubtitle}}</div>
    </div>
  </div>
</template>

<style scoped>
.banner-container {
  box-sizing: border-box;
  max-width: 460px;
}
</style>