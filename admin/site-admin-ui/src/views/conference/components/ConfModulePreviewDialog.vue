<template>
  <el-dialog
      v-model="showPreviewDialog"
      title="会务首页预览"
      width="460px"
      :before-close="() => { showPreviewDialog = false; }"
      class="preview-dialog"
  >
    <module-preview :preview-data="previewData"/>
    <template #footer>
      <div class="flex justify-center">
        <el-button @click="showPreviewDialog = false">
          关闭预览
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {computed, PropType} from "vue";
import ModulePreview from "@/views/conference/components/ModulePreview.vue";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  previewData: {
    type: Object as PropType<IConfPreviewData>,
    required: true
  }
})

const emits = defineEmits(['update:modelValue']);

const showPreviewDialog = computed({
  get() {
    return props.modelValue;
  },
  set(value: boolean) {
    emits('update:modelValue', value);
  }
});
</script>

<style scoped>
/* 移动端预览样式 */
.preview-dialog :deep(.el-dialog__body) {
  padding: 0;
}
</style>