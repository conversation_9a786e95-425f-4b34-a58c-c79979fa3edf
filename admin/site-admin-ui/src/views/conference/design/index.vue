<script setup lang="ts">
import {ref} from 'vue'

import ConfLocMap from "@/views/conference/components/ConfLocMap.vue";

const addrData = ref<IConfAddr[]>([])

</script>

<template>
  <div>
    <div>
      <el-button type="primary" icon="plus" plain >添加地址</el-button>
    </div>
    <el-table :data="addrData">
      <el-table-column label="会务地址" prop="confAddr" align="center"/>
      <el-table-column label="会务地址描述" prop="descInfo" align="center"/>
      <el-table-column label="地图定位"  align="center"/>
      <el-table-column label="操作" align="center">
        <template #default="{row}">

          <el-button type="primary" text size="small">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>

</template>

<style scoped>

</style>
                    