<script setup lang="ts">

import {useRoute} from "vue-router";
import {ref, watch, computed, type CSSProperties} from "vue";
import {getConfBasic} from "@/api/conference/basic";
import {
  delModuleRes,
  getModuleRes,
  listModuleDirRes,
  listModuleFileRes,
  saveModuleDirRes, saveModuleFileRes
} from "@/api/conference/module_res";
import {ElMessage, ElMessageBox} from "element-plus";
import {Calendar, Delete, Edit, Location, Picture, Plus, Loading} from "@element-plus/icons-vue";
import TailwindColorClassPicker from "@/components/TailwindColorClassPicker/index.vue";
// import ImageUpload from "@/components/ImageUpload/index.vue";

defineOptions({
  name: "AgendaDetail",
})
const imgHost = import.meta.env.VITE_APP_BASE_API
const moduleType = '座位图'
const route = useRoute();
const confId = ref<string>('')
const confBasic = ref<IConfBasic>()
// 目录资源列表
const moduleDirResList = ref<IConfModuleRes[]>([])
// 文件资源列表
const moduleFileResList = ref<IConfModuleRes[]>([])

// 资源表单
const moduleResForm = ref<IConfModuleRes>({})
const moduleResDialogVisible = ref(false)
// 当前选中的目录id
const currentDirResId = ref<string>('')
// 资源表单ref
const moduleResFormRef = ref()
// 保存loading
const saveLoading = ref(false)


// 获取会务信息
const getConf = async (confId: string) => {
  const {data} = await getConfBasic(confId)
  confBasic.value = data
}

// 获取会务模块内容目录资源
const getModuleDirResList = async () => {
  const {data} = await listModuleDirRes(confId.value, moduleType)
  moduleDirResList.value = data
  if (data.length > 0 && !currentDirResId.value) {
    currentDirResId.value = data[0].id!
  }
}

// 获取会务模块内容文件资源
const getModuleFileResList = async (dirId?: string) => {
  const {data} = await listModuleFileRes(confId.value, moduleType, dirId)
  moduleFileResList.value = data
}

// 获取会务模块内容资源
const getRes = async (id: string) => {
  const {data} = await getModuleRes(id)
  moduleResForm.value = data
}

// 删除会务模块内容资源
const delRes = async (id: string) => {
  await delModuleRes(id)
}

watch(() => route.query.confId, (val) => {
  if (val) {
    confId.value = String(val ?? '')
    getConf(confId.value)
    getModuleDirResList()
  }
}, {immediate: true})

const addRes = (resType: string, dirId?: string) => {
  moduleResForm.value = {
    confId: confId.value,
    moduleType: moduleType,
    resType: resType,
    dirTitleColorClass: '',
    dirBgColorClass: '',
    dirId: dirId,
    sort: 0,
  }
  moduleResDialogVisible.value = true
}

const editRes = async (id: string) => {
  await getRes(id)
  moduleResDialogVisible.value = true
}

const selectDir = (dirId: string) => {
  currentDirResId.value = dirId
}

watch(() => currentDirResId.value, () => {
  if (currentDirResId.value) {
    getModuleFileResList(currentDirResId.value)
  }
})

// 删除目录
const deleteDir = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除该目录吗？删除后目录下的所有资源也将被删除！', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await delRes(id)
    ElMessage.success('删除成功')
    await getModuleDirResList()
    // 如果删除的是当前选中的目录，清空选中状态
    if (currentDirResId.value === id) {
      currentDirResId.value = ''
      moduleFileResList.value = []
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 删除文件资源
const deleteFile = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除该资源吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await delRes(id)
    ElMessage.success('删除成功')
    await getModuleFileResList(currentDirResId.value)
  } catch (error) {
    // 用户取消删除
  }
}

// 保存会务模块内容资源
const saveModuleRes = async () => {
  let resType = moduleResForm.value.resType
  const api = resType === 'D' ? saveModuleDirRes : saveModuleFileRes
  moduleResFormRef.value.validate().then(async () => {
    if(resType === 'D'){
      if (!moduleResForm.value.dirBgColorClass && !moduleResForm.value.dirBgUrl) {
        ElMessage.error('目录背景颜色和背景图不能同时为空')
        return
      }
      moduleResForm.value.fileType = ''
    }
    if(resType === 'F'){
      // 资源后缀
      moduleResForm.value.fileType = moduleResForm.value.fileUrl?.substring(moduleResForm.value.fileUrl.lastIndexOf('.') + 1)
    }
    saveLoading.value = true
    try {
      await api(moduleResForm.value)
      ElMessage.success('保存成功')
      moduleResDialogVisible.value = false
      if(resType === 'D'){
        await getModuleDirResList()
      } else {
        await getModuleFileResList(currentDirResId.value)
      }
    } catch (e) {
      console.error('保存失败:', e)
      ElMessage.error('保存失败')
    } finally {
      saveLoading.value = false
    }
  });

}

const dialogTitle = computed(() => {
  if (moduleResForm.value?.resType === 'D') {
    return moduleResForm.value?.id ? '编辑目录' : '添加目录'
  } else {
    return moduleResForm.value?.id ? '编辑资源' : '添加资源'
  }
})

const getDirBackgroundStyle = (dirRes: IConfModuleRes): CSSProperties => {
  if (dirRes.dirBgUrl) {
    return {
      backgroundImage: `url(${imgHost}${dirRes.dirBgUrl})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    };
  }
  return {};
}
</script>

<template>
  <div class="p-4 h-full flex flex-col gap-4 bg-[#f0f2f5]">
    <!-- 主要内容区域 -->
    <div class="flex gap-4 flex-1">
      <!-- 左侧目录列表 -->
      <div class="w-80 flex flex-col">
        <el-card shadow="never" class="h-full">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg font-medium">{{confBasic?.name + '座位图'}}目录列表</span>
            </div>
          </template>

          <div class="space-y-3 overflow-y-auto" style="max-height: calc(100vh - 200px);">
            <!-- 添加目录按钮 -->
            <div
                class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors"
                @click="addRes('D')"
            >
              <el-icon class="text-2xl text-gray-400 mb-2">
                <Plus/>
              </el-icon>
              <div class="text-gray-500">添加新目录</div>
            </div>

            <!-- 目录列表 -->
            <div
                v-for="dirRes in moduleDirResList"
                :key="dirRes.id"
                class="directory-item group relative border rounded-lg p-3 cursor-pointer flex flex-col justify-center h-24 overflow-hidden"
                :class="{
                  'border-blue-500 selected': currentDirResId === dirRes.id,
                  'border-gray-200': currentDirResId !== dirRes.id,
                  [dirRes.dirBgColorClass!]: !dirRes.dirBgUrl
                }"
                :style="getDirBackgroundStyle(dirRes)"
                @click="selectDir(dirRes.id!)"
            >
              <!-- 选中状态遮罩层 -->
              <div
                v-if="currentDirResId === dirRes.id"
                class="selected-overlay absolute inset-0 bg-blue-500 bg-opacity-20 rounded-lg pointer-events-none"
              ></div>

              <!-- 选中状态指示器 -->
              <div
                v-if="currentDirResId === dirRes.id"
                class="selected-indicator absolute top-2 left-2 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center shadow-lg"
              >
                <div class="w-2 h-2 bg-white rounded-full"></div>
              </div>

              <!-- 悬停操作按钮 -->
              <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1 z-20 rounded-lg">
                <el-button
                    type="primary"
                    size="small"
                    circle
                    @click.stop="editRes(dirRes.id!)"
                    class="!w-6 !h-6 !min-h-6"
                >
                  <el-icon class="!text-xs">
                    <Edit/>
                  </el-icon>
                </el-button>
                <el-button
                    type="danger"
                    size="small"
                    circle
                    @click.stop="deleteDir(dirRes.id!)"
                    class="!w-6 !h-6 !min-h-6"
                >
                  <el-icon class="!text-xs">
                    <Delete/>
                  </el-icon>
                </el-button>
              </div>

              <div class="directory-title relative z-10 text-2xl font-semibold text-center"
                   :class="[
                     dirRes.dirTitleColorClass,
                     currentDirResId === dirRes.id ? 'selected' : ''
                   ]">
                {{ dirRes.dirTitle }}
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧资源展示区域 -->
      <div class="flex-1 flex flex-col">
        <el-card shadow="never" class="h-full">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg font-medium">资源管理</span>
              <div class="flex gap-2">
                <el-button
                    type="primary"
                    size="small"
                    @click="addRes('F', currentDirResId)"
                    :disabled="!currentDirResId"
                >
                  <el-icon>
                    <Plus/>
                  </el-icon>
                  添加资源
                </el-button>
              </div>
            </div>
          </template>

          <div v-if="!currentDirResId" class="text-center text-gray-500 py-20">
            请先选择一个目录
          </div>

          <div v-else class="h-full flex flex-col">
            <!-- 资源网格 -->
            <el-scrollbar class="flex-1" style="max-height: calc(100vh - 250px);">
              <div class="resource-grid p-2">
                <!-- 添加资源按钮 -->
                <div
                    class="resource-item add-item border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors"
                    @click="addRes('F', currentDirResId)"
                >
                  <el-icon class="text-3xl text-gray-400 mb-2">
                    <Plus/>
                  </el-icon>
                  <div class="text-gray-500 text-sm">添加资源</div>
                </div>

                <!-- 资源卡片 -->
                <div
                    v-for="fileRes in moduleFileResList"
                    :key="fileRes.id"
                    class="resource-item relative group border rounded-lg overflow-hidden hover:shadow-lg transition-shadow bg-gray-100"
                >
                  <div class="resource-container">
                    <el-image
                        :src="imgHost + fileRes.fileUrl"
                        fit="cover"
                        class="w-full h-full"
                        :preview-src-list="moduleFileResList.map(item => imgHost + item.fileUrl)"
                        :initial-index="moduleFileResList.findIndex(item => item.id === fileRes.id)"
                        loading="lazy"
                    />

                    <!-- 悬浮操作按钮 -->
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                      <el-button
                          type="primary"
                          size="small"
                          circle
                          @click.stop="editRes(fileRes.id!)"
                      >
                        <el-icon>
                          <Edit/>
                        </el-icon>
                      </el-button>
                      <el-button
                          type="danger"
                          size="small"
                          circle
                          @click.stop="deleteFile(fileRes.id!)"
                      >
                        <el-icon>
                          <Delete/>
                        </el-icon>
                      </el-button>
                    </div>

                    <!-- 资源信息标签 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs p-2 truncate">
                      <div class="font-medium">{{ fileRes.fileName }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </el-card>
      </div>
    </div>



    <el-dialog
        v-model="moduleResDialogVisible"
        :title="dialogTitle"
        width="600px"
        @close="moduleResFormRef?.resetFields()"
    >
      <el-form
          ref="moduleResFormRef"
          :model="moduleResForm"
          label-width="130px"
          :rules="{
            fileName: [{ required: true, message: '请输入资源名称', trigger: 'blur' }],
            fileUrl: [{ required: true, message: '请上传资源', trigger: 'change' }]
          }"
      >
        <div v-if="moduleResForm.resType === 'D'">
          <el-form-item label="目录名称" prop="dirTitle">
            <el-input v-model="moduleResForm.dirTitle" placeholder="请输入目录名称" />
          </el-form-item>

          <el-form-item label="目录名称颜色" prop="dirTitleColorClass">
            <tailwind-color-class-picker v-model="moduleResForm.dirTitleColorClass" type="text" />
          </el-form-item>

          <el-form-item label="目录背景颜色" prop="dirBgColorClass">
            <tailwind-color-class-picker v-model="moduleResForm.dirBgColorClass" type="background" />
          </el-form-item>

          <el-form-item label="目录背景图" prop="dirBgUrl">
            <ImageUpload v-model="moduleResForm.dirBgUrl" :limit="1" :file-size="5"/>
          </el-form-item>
        </div>
        <div v-if="moduleResForm.resType === 'F'">
          <el-form-item label="资源名称" prop="fileName">
            <el-input v-model="moduleResForm.fileName" placeholder="请输入资源名称" />
          </el-form-item>
          <el-form-item label="资源" prop="fileUrl">
            <ImageUpload v-model="moduleResForm.fileUrl" :limit="1" :file-size="5"/>
          </el-form-item>
        </div>
        <el-form-item label="排序">
          <el-input-number v-model="moduleResForm.sort" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="moduleResDialogVisible = false" :loading="saveLoading">取消</el-button>
        <el-button type="primary" @click="saveModuleRes" :loading="saveLoading">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
/* 目录项容器 - 防止动画导致滚动条 */
.directory-item {
  position: relative;
  transition: box-shadow 0.3s ease, border-color 0.3s ease;
}

.directory-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 选中状态遮罩层动画 - 只使用opacity动画 */
.selected-overlay {
  animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* 选中指示器动画 - 使用transform但不影响布局 */
.selected-indicator {
  animation: indicatorScale 0.3s ease-out;
  transform-origin: center;
}

@keyframes indicatorScale {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 文字阴影增强，确保在各种背景下都能清晰显示 */
.directory-title {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: text-shadow 0.3s ease;
}

.directory-title.selected {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 选中状态的边框增强效果 */
/*.directory-item.selected {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(0, 0, 0, 0.15);
}*/

/* 资源网格布局 */
.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 8px;
}

/* 资源项目 */
.resource-item {
  aspect-ratio: 1 / 1;
  position: relative;
  min-height: 200px;
  max-height: 250px;
}

/* 资源容器 */
.resource-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 添加按钮样式 */
.add-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .resource-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
  .resource-item {
    min-height: 180px;
    max-height: 220px;
  }
}

@media (max-width: 768px) {
  .resource-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }
  .resource-item {
    min-height: 150px;
    max-height: 180px;
  }
}
</style>