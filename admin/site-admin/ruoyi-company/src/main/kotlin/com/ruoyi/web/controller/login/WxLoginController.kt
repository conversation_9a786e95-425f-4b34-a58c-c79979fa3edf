package com.ruoyi.web.controller.login

import com.alibaba.fastjson2.JSONObject
import com.ruoyi.common.annotation.Anonymous
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.domain.entity.SysRole
import com.ruoyi.common.core.domain.entity.SysUser
import com.ruoyi.common.core.redis.RedisCache
import com.ruoyi.common.utils.RsaUtil
import com.ruoyi.common.utils.SecurityUtils
import com.ruoyi.common.utils.StringUtils
import com.ruoyi.common.utils.uuid.IdUtils
import com.ruoyi.framework.web.service.SysLoginService
import com.ruoyi.system.service.impl.SysRoleServiceImpl
import com.ruoyi.system.service.impl.SysUserServiceImpl
import com.ruoyi.web.domain.dto.wx.WxAccessTokenDto
import com.ruoyi.web.domain.dto.wx.WxBindInfoDto
import com.ruoyi.web.domain.dto.wx.WxUserInfoDto
import com.ruoyi.web.feign.WxService
import com.ruoyi.web.vo.LoginBody
import jakarta.servlet.http.HttpServletResponse
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.stereotype.Controller
import org.springframework.transaction.annotation.Transactional
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.ModelAndView
import java.util.*
import java.util.concurrent.TimeUnit


@Controller
@RequestMapping("/company/wx")
@Transactional(rollbackFor = [Throwable::class])
open class WxLoginController(
    val redisCache: RedisCache,
    val wxService: WxService,
    val sysUserServiceImpl: SysUserServiceImpl,
    val sysLoginService: SysLoginService,
    val sysRoleServiceImpl: SysRoleServiceImpl
) {

    @Value("\${wx.appid}")
    lateinit var appId: String

    @Value("\${wx.appSecret}")
    lateinit var appSecret: String

    @Value("\${wx.authorize_url}")
    lateinit var authorizeUrl: String

    @Value("\${wx.bind_redirect_url}")
    lateinit var bindRedirectUrl: String

    @Value("\${wx.login_redirect_url}")
    lateinit var loginRedirectUrl: String


    data class LoginVo(val uuid: String, val password: String)
    @Anonymous
    @ResponseBody
    @PostMapping("/login")
    open fun loginWx(@RequestBody data: String): AjaxResult {
        val vo: LoginVo = JSONObject.parseObject(
            RsaUtil.decryptByPrivateKey(data, RsaUtil.defaultPrivateKey),
            LoginVo::class.java
        )
        val dto = redisCache.getCacheObject<WxBindInfoDto>(vo.uuid)
        if (StringUtils.isEmpty(dto.openid)) {
            return AjaxResult.error("未查询到openId，请重新扫码登录")
        }
        val ajax = AjaxResult.success()
        val token = sysLoginService.loginByWx(dto.openid, vo.password)
        ajax[Constants.TOKEN] = token
        return ajax
    }

    @ResponseBody
    @GetMapping("getBindId")
    @PreAuthorize("@ss.hasPermi('company:user:bindWx')")
    open fun getBindId(): AjaxResult {
        val wxBindInfoDto = WxBindInfoDto(
            userId = SecurityUtils.getUserId(),
            openid = ""
        )
        val uuid = IdUtils.fastSimpleUUID()
        redisCache.setCacheObject(uuid, wxBindInfoDto, 10, TimeUnit.MINUTES)
        return AjaxResult.success("id", uuid)
    }

    @Anonymous
    @ResponseBody
    @GetMapping("getLoginId")
    open fun getLoginId(): AjaxResult {
        val wxBindInfoDto = WxBindInfoDto(
            openid = ""
        )
        val uuid = IdUtils.fastSimpleUUID()
        redisCache.setCacheObject(uuid, wxBindInfoDto, 10, TimeUnit.MINUTES)
        return AjaxResult.success("id", uuid)
    }

    @Anonymous
    @ResponseBody
    @GetMapping("getAuthorizationStatus")
    open fun getAuthorizationStatus(@RequestParam("uuid") uuid: String): AjaxResult {
        val wxBindInfo = redisCache.getCacheObject<WxBindInfoDto?>(uuid)
        if (StringUtils.isNotEmpty(wxBindInfo?.openid)) {
            return AjaxResult.success("授权成功", 1)
        }
        return AjaxResult.success("", 0)
    }

    @Anonymous
    @GetMapping("bind/{id}")
    open fun bind(@PathVariable("id") id: String, response: HttpServletResponse) {
        val wxBindInfo = redisCache.getCacheObject<WxBindInfoDto?>(id)
        if (wxBindInfo?.userId == null) {
            response.contentType = "text/html;charset=UTF-8"
            response.writer.print("<html><body><p>二维码已过期</p></body></html>")
        } else {
            val wechatAuthUrl = "${authorizeUrl}?appid=${appId}&redirect_uri=${bindRedirectUrl}&response_type=code&scope=snsapi_userinfo&state=${id}#wechat_redirect"
            response.status = HttpServletResponse.SC_FOUND // 302
            response.sendRedirect(wechatAuthUrl)
        }

    }

    @Anonymous
    @GetMapping("login/{id}")
    open fun login(@PathVariable("id") id: String, response: HttpServletResponse) {
        val wxBindInfo = redisCache.getCacheObject<WxBindInfoDto?>(id)
        if (wxBindInfo == null) {
            response.contentType = "text/html;charset=UTF-8"
            response.writer.print("<html><body><p>二维码已过期</p></body></html>")
        } else {
            val wechatAuthUrl = "${authorizeUrl}?appid=${appId}&redirect_uri=${loginRedirectUrl}&response_type=code&scope=snsapi_base&state=${id}#wechat_redirect"
            response.status = HttpServletResponse.SC_FOUND // 302
            response.sendRedirect(wechatAuthUrl)
        }

    }

    @Anonymous
    @GetMapping("bind/authorized")
    open fun bindAuthorized(@RequestParam("state") state: String, @RequestParam("code") code: String, model: Model): String {
        val accessInfo = JSONObject.parseObject(wxService.getAccessToken(appId, appSecret, code), WxAccessTokenDto::class.java)

        if (StringUtils.isEmpty(accessInfo?.access_token)) {
            model.addAttribute("error_message", "获取微信access_token失败，${JSONObject.toJSONString(accessInfo)}")
            return "wx/error"
        }
        val wxUserInfo = JSONObject.parseObject(wxService.getUserInfo(accessInfo!!.access_token, accessInfo.openid), WxUserInfoDto::class.java)
        if (StringUtils.isEmpty(wxUserInfo?.openid)) {
            model.addAttribute("error_message", "获取微信用户信息失败，${JSONObject.toJSONString(wxUserInfo)}")
            return "wx/error"
        }
        if (wxUserInfo?.nickname.equals("微信用户")) {
            model.addAttribute("error_message", "微信用户未授权，请点击下方授权信息!")
            return "wx/authorized"
        }
        val wxUser = sysUserServiceImpl.selectUserByUserName(wxUserInfo.openid)
        if (wxUser != null) {
            model.addAttribute("error_message", "当前微信用户已经绑定，请移除后再绑定，${JSONObject.toJSONString(wxUserInfo)}")
            return "wx/error"
        }
        val wxBindInfo = redisCache.getCacheObject<WxBindInfoDto>(state)
        if (wxBindInfo == null) {
            model.addAttribute("error_message", "微信绑定状态已过期")
            return "wx/error"
        }
        val existUserList = sysUserServiceImpl.selectCaSubUserList(wxBindInfo.userId, "wx")
        if (existUserList.size >= 3) {
            model.addAttribute("error_message", "已绑定3个微信，请先解绑再绑定新的微信")
            return "wx/error"
        }

        // 更新redis缓存状态
        wxBindInfo.openid = wxUserInfo!!.openid
        redisCache.setCacheObject(state, wxBindInfo, 1, TimeUnit.MINUTES)



        if (sysUserServiceImpl.selectUserByUserName(wxBindInfo.openid) == null) {
            // 新建用户
            val caUser = sysUserServiceImpl.selectUserById(wxBindInfo.userId)
            val user = SysUser().apply {
                parentId = wxBindInfo.userId
                deptId = caUser.deptId
                userName = wxUserInfo.openid
                nickName = wxUserInfo.nickname
                userType = "wx"
                sex = if (wxUserInfo.sex == "1") "0" else if (wxUserInfo.sex == "2") "1" else "2"
                avatar = wxUserInfo.headimgurl
                password = caUser.password
                status = "0"
                createBy = caUser.userName
                createTime = Date()
            }
            sysUserServiceImpl.saveOrUpdate(user)

            val role = sysRoleServiceImpl.selectRoleByRoleKey("COMPANY_USER_WX");
            if (role != null) {
                sysUserServiceImpl.insertUserRole(user.userId, arrayOf(role.roleId))
            }

            val parentUser = sysUserServiceImpl.selectUserById(wxBindInfo.userId);

            val parentRoles = parentUser.roles;

            for (parentRole in parentRoles) {
                if (parentRole.roleKey.equals("COMPANY_USER_CA")){
                    continue
                }
                sysUserServiceImpl.insertUserRole(user.userId, arrayOf(parentRole.roleId))
            }
        }
        return "wx/success"
    }

    @Anonymous
    @GetMapping("login/authorized")
    open fun loginAuthorized(@RequestParam("state") state: String, @RequestParam("code") code: String, model: Model): String {
        val accessInfo = JSONObject.parseObject(wxService.getAccessToken(appId, appSecret, code), WxAccessTokenDto::class.java)
        if (StringUtils.isEmpty(accessInfo?.access_token)) {
            model.addAttribute("error_message", "获取微信access_token失败，${JSONObject.toJSONString(accessInfo)}")
            return "wx/error"
        }
        val wxBindInfo = redisCache.getCacheObject<WxBindInfoDto>(state)
        if (wxBindInfo == null) {
            model.addAttribute("error_message", "微信状态已过期")
            return "wx/error"
        }
        val user = sysUserServiceImpl.selectUserByUserName(accessInfo.openid)
        if (user == null) {
            model.addAttribute("error_message", "当前微信未绑定账号，请联系管理员")
            return "wx/error"
        }

        // 更新redis缓存状态
        wxBindInfo.openid = accessInfo.openid
        redisCache.setCacheObject(state, wxBindInfo, 10, TimeUnit.MINUTES)

        return "wx/success"
    }
}