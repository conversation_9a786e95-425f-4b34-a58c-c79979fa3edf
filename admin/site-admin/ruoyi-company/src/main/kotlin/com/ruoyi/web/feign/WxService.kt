package com.ruoyi.web.feign

import com.alibaba.fastjson2.JSONObject
import com.ruoyi.web.domain.dto.wx.WxAccessTokenDto
import com.ruoyi.web.domain.dto.wx.WxUserInfoDto
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam

@FeignClient(value = "wx-oauth2-service", url = "\${wx.api_url}")
interface WxService {

    @GetMapping("/oauth2/access_token")
    fun getAccessToken(
        @RequestParam("appid") appid: String,
        @RequestParam("secret") secret: String,
        @RequestParam("code") code: String,
        @RequestParam("grant_type") grantType: String = "authorization_code"
    ): String

    @GetMapping("/userinfo")
    fun getUserInfo(
        @RequestParam("access_token") access_token: String,
        @RequestParam("openid") openid: String,
        @RequestParam("lang") lang: String = "zh_CN"
    ): String
}