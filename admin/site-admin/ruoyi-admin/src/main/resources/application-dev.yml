# 项目相关配置
ruoyi:
    # 名称
    name: RuoYi
    # 版本
    version: 3.8.7
    # 版权年份
    copyrightYear: 2023
    # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
    profile: /Users/<USER>/file/e/file/ruoyi/uploadPath
    # 获取ip地址开关
    addressEnabled: false
    # 验证码类型 math 数字计算 char 字符验证
    captchaType: math

# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
#        driverClassName: com.mysql.cj.jdbc.Driver
        driverClassName: dm.jdbc.driver.DmDriver
        druid:
            # 主库数据源
#            master:
#                url: ************************************************************************************************************************************************
#                username: ktz
#                password: Metabc#123
            master:
#                url: ************************************************************************************************************************************************
#                username: ktz
#                password: Metabc#123
                url: jdbc:dm://*************:5236?schema=hdbweb
                username: hdbweb
                password: Metabc123
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url: 
                username: 
                password: 
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter: 
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true

# 短信服务
sms:
    # 地址
    url: https://mas.cloud.sh.gov.cn
    # 签名
    sign: 【上海市核电办公室】
    # 用户名
    username: JXHDBGS
    # 密码
    password: GQs2@fCRgl@w

oa:
    # oa 地址
    url: http://hdb.metinform.cn

wx:
    appid: wx5383ef437e75a11f
    appSecret: 518f1ef94ddfbcededcabab11caf418f
    authorize_url: https://open.weixin.qq.com/connect/oauth2/authorize
    api_url: https://api.weixin.qq.com/sns
    bind_redirect_url: http://cdhjm123.6655.la/hdbmhht/company/wx/bind/authorized
    login_redirect_url: http://cdhjm123.6655.la/hdbmhht/company/wx/login/authorized

ca:
    gateway: https://183.194.243.82/clientgateway/
    appid: shhdb_728_123456
    apiname: 6bfa95b4-4fec-4d76-89bd-485b7ba97c35
    secretKey: ff09b3788c824c4b9f4c76a1a1629441
    redirect_url: http://192.168.249.194:3002/hdb/company/login/ca