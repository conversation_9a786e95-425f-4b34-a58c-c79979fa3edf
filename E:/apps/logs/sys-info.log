13:04:59.473 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
13:04:59.521 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 40114 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
13:04:59.523 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
13:05:02.135 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
13:05:03.306 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
13:05:03.308 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:05:03.308 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
13:05:03.353 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
13:05:04.241 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
13:05:04.806 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
13:05:06.648 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
13:05:08.880 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:05:08.894 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:05:08.895 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:05:08.895 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:05:08.896 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:05:08.896 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:05:08.896 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:05:08.896 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@712de793
13:05:13.241 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
13:05:14.922 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
13:05:15.084 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
13:05:15.084 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
13:05:15.084 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
13:05:15.455 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
13:05:15.543 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
13:05:15.546 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:05:15.547 [Thread-8] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
13:05:15.547 [Thread-9] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
13:05:15.547 [Thread-10] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
13:05:15.672 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
13:05:15.943 [restartedMain] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: 10830565-944d-11f0-b028-2a8d686d9ea7, key: news_release_audit_process, name: news_release_audit_process }
13:05:21.065 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
13:05:21.077 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
13:05:21.186 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
13:05:21.186 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
13:05:21.282 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
13:05:21.282 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
13:06:00.012 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
13:06:00.013 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
13:06:00.092 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
13:06:00.093 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:06:00.114 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 61.175 seconds (process running for 63.097)
13:06:00.730 [RMI TCP Connection(12)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:09:23.943 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Success][登录成功]
