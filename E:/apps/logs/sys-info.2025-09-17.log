10:38:26.992 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
10:38:27.031 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 96412 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
10:38:27.031 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
10:38:29.397 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:38:30.543 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
10:38:30.545 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:38:30.545 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
10:38:30.601 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
10:38:31.492 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
10:38:32.058 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
10:38:33.991 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
10:38:36.251 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:38:36.263 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:38:36.263 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:38:36.264 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:38:36.264 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:38:36.264 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:38:36.265 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:38:36.265 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@63b0c8c7
10:38:40.554 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
10:38:42.201 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
10:38:42.364 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
10:38:42.364 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
10:38:42.364 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
10:38:42.741 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
10:38:42.900 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
10:38:42.903 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
10:38:42.904 [Thread-8] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
10:38:42.904 [Thread-9] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
10:38:42.904 [Thread-10] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
10:38:43.098 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
10:38:43.483 [restartedMain] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: 6d52c826-936f-11f0-a8a1-2a8d686d9ea7, key: news_release_audit_process, name: news_release_audit_process }
10:38:48.747 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
10:38:48.758 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
10:38:48.934 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
10:38:48.935 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
10:38:49.084 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
10:38:49.084 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
10:39:34.172 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
10:39:34.172 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
10:39:34.288 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
10:39:34.289 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:39:34.310 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 67.711 seconds (process running for 68.93)
10:39:34.797 [RMI TCP Connection(1)-*************] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:40:52.913 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163,1757990798;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
10:40:59.424 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Success][登录成功]
11:08:00.688 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
11:08:00.692 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
11:08:00.692 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
11:08:00.692 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
11:08:00.692 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
11:08:00.692 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
11:08:00.692 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
11:08:00.693 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
11:08:00.693 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:11:55.256 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
11:11:55.297 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 99391 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
11:11:55.298 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
11:11:57.715 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
11:11:58.793 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
11:11:58.795 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:11:58.795 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
11:11:58.857 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
11:11:59.720 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
11:12:00.278 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
11:12:02.184 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
11:12:04.492 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:12:04.503 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:12:04.503 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:12:04.503 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:12:04.504 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:12:04.504 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:12:04.504 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:12:04.504 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@45c75686
11:12:08.892 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
11:12:10.585 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
11:12:10.746 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
11:12:10.746 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
11:12:10.747 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
11:12:11.130 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
11:12:11.188 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
11:12:11.190 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
11:12:11.191 [Thread-8] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
11:12:11.191 [Thread-9] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
11:12:11.191 [Thread-10] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
11:12:11.315 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,90] - An existing deployment of version 1 matching the current one was found, no need to deploy again.
11:12:16.467 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
11:12:16.476 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
11:12:16.777 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
11:12:16.777 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
11:12:16.862 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
11:12:16.862 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
11:12:53.941 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
11:12:53.942 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
11:12:53.993 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
11:12:53.993 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:12:54.011 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 59.172 seconds (process running for 60.113)
11:12:54.752 [RMI TCP Connection(4)-*************] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:22:43.004 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163,1757990798;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
13:56:43.717 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
13:56:43.721 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
13:56:43.721 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
13:56:43.721 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
13:56:43.721 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
13:56:43.721 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
13:56:43.721 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
13:56:43.721 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
13:56:43.724 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
13:56:43.834 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,212] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:56:43.834 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,103] - {} stopped resetting expired jobs
13:56:43.834 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,119] - {} stopped async job due acquisition
13:56:43.834 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,119] - {} stopped async job due acquisition
13:56:43.848 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
13:56:43.849 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
13:56:43.850 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
13:56:43.852 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
13:56:43.869 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
13:56:43.879 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
13:56:48.534 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
13:56:48.570 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 6682 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
13:56:48.571 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
13:56:51.099 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
13:56:52.203 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
13:56:52.205 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:56:52.205 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
13:56:52.309 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
13:56:53.217 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
13:56:53.776 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
13:56:55.624 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
13:56:57.925 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:56:57.938 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:56:57.939 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:56:57.939 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:56:57.940 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:56:57.940 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:56:57.940 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:56:57.940 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7b5f0985
13:57:02.281 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
13:57:03.937 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
13:57:04.132 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
13:57:04.133 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
13:57:04.133 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
13:57:04.484 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
13:57:04.555 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
13:57:04.557 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:57:04.558 [Thread-8] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
13:57:04.558 [Thread-9] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
13:57:04.558 [Thread-10] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
13:57:04.709 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,90] - An existing deployment of version 1 matching the current one was found, no need to deploy again.
13:57:09.856 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
13:57:09.866 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
13:57:10.233 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
13:57:10.234 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
13:57:10.359 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
13:57:10.360 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
13:57:26.852 [tomcat-handler-1] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163,1757990798;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
13:57:26.852 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163,1757990798;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
13:57:26.891 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:57:47.692 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
13:57:47.693 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
13:57:47.811 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
13:57:47.811 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:57:47.831 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 59.723 seconds (process running for 61.24)
14:18:08.719 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:18:08.720 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:18:08.720 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:18:08.720 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:18:08.720 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:18:08.720 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:18:08.720 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:18:08.720 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:18:08.720 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:18:08.788 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,212] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:18:08.788 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,119] - {} stopped async job due acquisition
14:18:08.788 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,119] - {} stopped async job due acquisition
14:18:08.788 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,103] - {} stopped resetting expired jobs
14:18:08.795 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:18:08.795 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:18:08.795 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:18:08.795 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:18:08.806 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
14:18:08.813 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
14:18:25.029 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
14:18:25.119 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 7790 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
14:18:25.121 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
14:18:27.868 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:18:28.964 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
14:18:28.965 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:18:28.966 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
14:18:29.026 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
14:18:29.864 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
14:18:30.493 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
14:18:32.602 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
14:18:34.754 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:18:34.766 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:18:34.766 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:18:34.767 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:18:34.767 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:18:34.767 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:18:34.768 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:18:34.768 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2a82d2e6
14:18:39.284 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
14:18:41.043 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
14:18:41.209 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
14:18:41.210 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:18:41.210 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:18:41.603 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:18:41.660 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
14:18:41.662 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:18:41.663 [Thread-9] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
14:18:41.663 [Thread-10] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
14:18:41.663 [Thread-11] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
14:18:41.779 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,90] - An existing deployment of version 1 matching the current one was found, no need to deploy again.
14:18:46.982 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
14:18:47.008 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:18:47.378 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:18:47.378 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:18:47.467 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:18:47.467 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:19:14.004 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:19:14.005 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:19:14.064 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:19:14.064 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:19:14.083 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 49.704 seconds (process running for 52.105)
14:19:15.105 [RMI TCP Connection(4)-*************] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:19:18.218 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163,1757990798;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
14:20:07.015 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:20:07.015 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:20:07.015 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:20:07.015 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:20:07.016 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:20:07.016 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:20:07.016 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:20:07.016 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:20:07.016 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:20:07.102 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,212] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:20:07.102 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,119] - {} stopped async job due acquisition
14:20:07.102 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,103] - {} stopped resetting expired jobs
14:20:07.102 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,119] - {} stopped async job due acquisition
14:20:07.105 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:20:07.105 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:20:07.105 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:20:07.105 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:20:07.117 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
14:20:07.120 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
14:23:03.143 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
14:23:03.186 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 8044 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
14:23:03.187 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
14:23:05.718 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:23:06.797 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
14:23:06.799 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:23:06.799 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
14:23:06.862 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
14:23:07.706 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
14:23:08.265 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
14:23:10.300 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
14:23:12.511 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:23:12.524 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:23:12.524 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:23:12.525 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:23:12.525 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:23:12.526 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:23:12.526 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:23:12.526 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7754ffee
14:23:16.956 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
14:23:18.661 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
14:23:18.826 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
14:23:18.826 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:23:18.826 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:23:19.207 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:23:19.276 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
14:23:19.278 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:23:19.279 [Thread-9] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
14:23:19.280 [Thread-10] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
14:23:19.280 [Thread-11] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
14:23:19.515 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,90] - An existing deployment of version 1 matching the current one was found, no need to deploy again.
14:23:24.699 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
14:23:24.708 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:23:25.018 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:23:25.019 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:23:25.153 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:23:25.154 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:23:37.402 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163,1757990798;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
14:23:37.412 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:24:17.726 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:24:17.727 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:24:17.824 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:24:17.825 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:24:17.846 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 75.17 seconds (process running for 76.814)
14:24:17.849 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:24:17.849 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:24:17.850 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:24:17.850 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:24:17.850 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:24:17.850 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:24:17.850 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:24:17.851 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:24:17.851 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:24:17.894 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,212] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:24:17.894 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,119] - {} stopped async job due acquisition
14:24:17.894 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,119] - {} stopped async job due acquisition
14:24:17.894 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,103] - {} stopped resetting expired jobs
14:24:17.896 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:24:17.897 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:24:17.897 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:24:17.897 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:24:17.909 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
14:24:17.912 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
14:24:25.409 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
14:24:25.456 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 8145 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
14:24:25.457 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
14:24:27.912 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:24:28.992 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
14:24:28.994 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:24:28.994 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
14:24:29.047 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
14:24:29.888 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
14:24:30.476 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
14:24:32.501 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
14:24:34.782 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:24:34.796 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:24:34.796 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:24:34.797 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:24:34.798 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:24:34.798 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:24:34.798 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:24:34.799 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@402630a3
14:24:39.231 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
14:24:41.027 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
14:24:41.199 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
14:24:41.200 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:24:41.200 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:24:41.581 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:24:41.659 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
14:24:41.660 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:24:41.661 [Thread-9] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
14:24:41.661 [Thread-10] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
14:24:41.662 [Thread-11] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
14:24:41.785 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,90] - An existing deployment of version 1 matching the current one was found, no need to deploy again.
14:24:47.248 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
14:24:47.259 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:24:47.706 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:24:47.707 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:24:47.814 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:24:47.814 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:25:24.519 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:25:24.520 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:25:24.670 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:25:24.671 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:25:24.692 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 59.653 seconds (process running for 61.248)
14:25:25.577 [RMI TCP Connection(4)-*************] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:26:53.916 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163,1757990798;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
14:30:59.665 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:30:59.666 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:30:59.666 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:30:59.667 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:30:59.667 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:30:59.667 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:30:59.667 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
14:30:59.667 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
14:30:59.668 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:30:59.724 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,212] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:30:59.724 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,119] - {} stopped async job due acquisition
14:30:59.724 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,119] - {} stopped async job due acquisition
14:30:59.724 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,103] - {} stopped resetting expired jobs
14:30:59.730 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:30:59.730 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:30:59.731 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:30:59.731 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:30:59.744 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
14:30:59.748 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
14:31:05.643 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
14:31:05.684 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 8497 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
14:31:05.685 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
14:31:07.980 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:31:09.038 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
14:31:09.039 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:31:09.040 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
14:31:09.098 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
14:31:09.903 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
14:31:10.472 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
14:31:12.273 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
14:31:14.385 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:31:14.397 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:31:14.397 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:31:14.398 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:31:14.399 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:31:14.399 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:31:14.399 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:31:14.399 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4b4c2e4
14:31:18.858 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
14:31:20.572 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
14:31:20.738 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
14:31:20.738 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:31:20.738 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:31:21.121 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:31:21.192 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
14:31:21.194 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:31:21.196 [Thread-9] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
14:31:21.196 [Thread-10] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
14:31:21.196 [Thread-11] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
14:31:21.339 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,90] - An existing deployment of version 1 matching the current one was found, no need to deploy again.
14:31:26.980 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
14:31:26.990 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:31:27.265 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:31:27.266 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:31:27.451 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:31:27.451 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:31:28.718 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163,1757990798;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
14:31:28.731 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:32:01.718 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:32:01.719 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:32:01.810 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:32:01.811 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:32:01.829 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 56.563 seconds (process running for 58.065)
15:11:31.551 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
15:11:31.555 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
15:11:31.555 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
15:11:31.555 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
15:11:31.556 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
15:11:31.556 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
15:11:31.556 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
15:11:31.556 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
15:11:31.557 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:11:31.728 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,212] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
15:11:31.728 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,119] - {} stopped async job due acquisition
15:11:31.728 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,119] - {} stopped async job due acquisition
15:11:31.728 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,103] - {} stopped resetting expired jobs
15:11:31.741 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:11:31.741 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:11:31.741 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:11:31.743 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:11:31.770 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
15:11:31.788 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
15:11:40.582 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
15:11:40.625 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 10524 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
15:11:40.626 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
15:11:43.050 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
15:11:44.149 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
15:11:44.151 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:11:44.152 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
15:11:44.201 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
15:11:45.102 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
15:11:45.757 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
15:11:47.848 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
15:11:50.157 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:11:50.170 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:11:50.171 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:11:50.171 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:11:50.172 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:11:50.172 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:11:50.172 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:11:50.172 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4b0af192
15:11:54.784 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
15:11:56.548 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
15:11:56.777 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
15:11:56.778 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
15:11:56.778 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
15:11:57.252 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
15:11:57.328 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
15:11:57.329 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
15:11:57.331 [Thread-10] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
15:11:57.331 [Thread-11] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
15:11:57.331 [Thread-9] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
15:11:57.441 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,90] - An existing deployment of version 1 matching the current one was found, no need to deploy again.
15:12:04.244 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
15:12:04.257 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
15:12:04.600 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
15:12:04.600 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
15:12:04.698 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
15:12:04.698 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
15:12:11.735 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163,1757990798;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
15:12:11.756 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:12:29.852 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
15:12:29.853 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
15:12:29.945 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
15:12:29.945 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:12:29.963 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 49.764 seconds (process running for 51.505)
18:07:23.880 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
18:07:23.881 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
18:07:23.881 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
18:07:23.881 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
18:07:23.881 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
18:07:23.881 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
18:07:23.881 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
18:07:23.881 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
18:07:23.882 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
18:07:23.951 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,212] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
18:07:23.952 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,119] - {} stopped async job due acquisition
18:07:23.952 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,103] - {} stopped resetting expired jobs
18:07:23.952 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,119] - {} stopped async job due acquisition
18:07:23.960 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
18:07:23.960 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
18:07:23.960 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
18:07:23.960 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
18:07:23.971 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
18:07:23.976 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
